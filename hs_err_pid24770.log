#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x0000000000000000, pid=24770, tid=0x0000000000004113
#
# JRE version: OpenJDK Runtime Environment (8.0) (build 1.8.0_282b08-internal-202101310908-dcevm8u282b08)
# Java VM: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26 mixed mode bsd-amd64 compressed oops)
# Problematic frame:
# C  0x0000000000000000
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://github.com/dcevm/dcevm/issues
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  T H R E A D  ---------------

Current thread (0x00007f78c0013800):  JavaThread "SIGINT handler" daemon [_thread_in_native, id=16659, stack(0x000000030cdf2000,0x000000030cef2000)]

siginfo: si_signo: 11 (SIGSEGV), si_code: 1 (SEGV_MAPERR), si_addr: 0x0000000000000000

Registers:
RAX=0x0000000000000000, RBX=0x00000001235ba710, RCX=0x0000000000000028, RDX=0x0000000000000000
RSP=0x000000030cef18c8, RBP=0x000000030cef1910, RSI=0x000000030cef1920, RDI=0x00007f78c00139e0
R8 =0x0000600007bf12c0, R9 =0x00000001235ba710, R10=0x000000010f1743f8, R11=0x000000010d6e5ef2
R12=0x0000000000000000, R13=0x00000001235ba710, R14=0x000000030cef1928, R15=0x00007f78c0013800
RIP=0x0000000000000000, EFLAGS=0x0000000000000287, ERR=0x0000000000000004
  TRAPNO=0x000000000000000e

Top of Stack: (sp=0x000000030cef18c8)
0x000000030cef18c8:   000000010f174424 000000030cef18d0
0x000000030cef18d8:   00000001235ba710 000000030cef1928
0x000000030cef18e8:   00000001235bae30 0000000000000000
0x000000030cef18f8:   00000001235ba710 0000000000000000
0x000000030cef1908:   000000030cef1930 000000030cef1980
0x000000030cef1918:   000000010f15d07d 00000006408238a8
0x000000030cef1928:   000000010f16b1b8 0000000000000001
0x000000030cef1938:   00000006408238a8 000000030cef1930
0x000000030cef1948:   00000001235bab3f 000000030cef19b0
0x000000030cef1958:   00000001235bae30 0000000000000000
0x000000030cef1968:   00000001235bab90 000000030cef1930
0x000000030cef1978:   000000030cef19b0 000000030cef19f8
0x000000030cef1988:   000000010f15d07d 0000000000000000
0x000000030cef1998:   0000000000000000 00000006408238a8
0x000000030cef19a8:   0000000000000000 0000000000000082
0x000000030cef19b8:   000000030cef19b8 00000001200e5cb0
0x000000030cef19c8:   000000030cef1a10 00000001200e5dc8
0x000000030cef19d8:   0000000000000000 00000001200e5cc0
0x000000030cef19e8:   000000030cef19b0 000000030cef1a08
0x000000030cef19f8:   000000030cef1a58 000000010f15d0c2
0x000000030cef1a08:   000000064097a298 000000064087f028
0x000000030cef1a18:   000000030cef1a18 0000000129dd0860
0x000000030cef1a28:   000000030cef1a68 0000000129dd0908
0x000000030cef1a38:   0000000000000000 0000000129dd0870
0x000000030cef1a48:   000000030cef1a08 000000030cef1a70
0x000000030cef1a58:   000000030cef1b20 0000000110490094
0x000000030cef1a68:   000000058606a8f0 000000011ff87bb8
0x000000030cef1a78:   00007f78c0013800 000000011ff87bb8
0x000000030cef1a88:   00007f78c0013800 000000058606a908
0x000000030cef1a98:   000000010d498e36 000000030cef1b20
0x000000030cef1aa8:   000000010f1554e7 000000010f1554e7
0x000000030cef1ab8:   000000058606a908 0000000300001fa0 

Instructions: (pc=0x0000000000000000)
0xffffffffffffffe0:   

Register to memory mapping:

RAX=0x0000000000000000 is an unknown value
RBX={method} {0x00000001235ba710} 'beforeHalt' '()V' in 'java/lang/Shutdown'
RCX=0x0000000000000028 is an unknown value
RDX=0x0000000000000000 is an unknown value
RSP=0x000000030cef18c8 is pointing into the stack for thread: 0x00007f78c0013800
RBP=0x000000030cef1910 is pointing into the stack for thread: 0x00007f78c0013800
RSI=0x000000030cef1920 is pointing into the stack for thread: 0x00007f78c0013800
RDI=0x00007f78c00139e0 is an unknown value
R8 =0x0000600007bf12c0 is an unknown value
R9 ={method} {0x00000001235ba710} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R10=0x000000010f1743f8 is at code_begin+1528 in an Interpreter codelet
method entry point (kind = native)  [0x000000010f173e00, 0x000000010f174d20]  3872 bytes
R11=0x000000010d6e5ef2: throw_unsatisfied_link_error+0 in /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib at 0x000000010d30c000
R12=0x0000000000000000 is an unknown value
R13={method} {0x00000001235ba710} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R14=0x000000030cef1928 is pointing into the stack for thread: 0x00007f78c0013800
R15=0x00007f78c0013800 is a thread


Stack: [0x000000030cdf2000,0x000000030cef2000],  sp=0x000000030cef18c8,  free space=1022k
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.Shutdown.beforeHalt()V+0
j  java.lang.Shutdown.exit(I)V+95
j  java.lang.Terminator$1.handle(Lsun/misc/Signal;)V+8
j  sun.misc.Signal$1.run()V+8
J 8336 C1 java.lang.Thread.run()V (17 bytes) @ 0x0000000110490094 [0x0000000110490040+0x54]
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
=>0x00007f78c0013800 JavaThread "SIGINT handler" daemon [_thread_in_native, id=16659, stack(0x000000030cdf2000,0x000000030cef2000)]
  0x00007f78d90ef000 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=37463, stack(0x000000030dc1c000,0x000000030dd1c000)]
  0x00007f7890814000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=52023, stack(0x00000003109a3000,0x0000000310aa3000)]
  0x00007f78da53c800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_in_native, id=43143, stack(0x000000030f567000,0x000000030f667000)]
  0x00007f78da2ee000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=56743, stack(0x000000030f464000,0x000000030f564000)]
  0x00007f78da3d9800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=32987, stack(0x000000030ed4f000,0x000000030ee4f000)]
  0x00007f78c7b1e800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=39211, stack(0x000000030eb49000,0x000000030ec49000)]
  0x00007f78d884a000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=56079, stack(0x000000030e943000,0x000000030ea43000)]
  0x00007f78d90b0800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=52339, stack(0x000000030e840000,0x000000030e940000)]
  0x00007f78d8811800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=36967, stack(0x000000030e12b000,0x000000030e22b000)]
  0x00007f78d886e800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=63355, stack(0x000000030ec4c000,0x000000030ed4c000)]
  0x00007f7890027000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=42939, stack(0x000000030e73d000,0x000000030e83d000)]
  0x00007f7891048800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=52779, stack(0x0000000310daf000,0x0000000310eaf000)]
  0x00007f7890026000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=54087, stack(0x0000000310cac000,0x0000000310dac000)]
  0x00007f7890013800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=54299, stack(0x000000031069a000,0x000000031079a000)]
  0x00007f78d9d66000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=36079, stack(0x0000000310ba9000,0x0000000310ca9000)]
  0x00007f78d03f9000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=50439, stack(0x000000030ea46000,0x000000030eb46000)]
  0x00007f78da2db000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=51223, stack(0x00000003108a0000,0x00000003109a0000)]
  0x00007f78d0d0b800 JavaThread "DestroyJavaVM" [_thread_blocked, id=4611, stack(0x000000030c7d5000,0x000000030c8d5000)]
  0x00007f7892984800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=57783, stack(0x000000031079d000,0x000000031089d000)]
  0x00007f78d03ce800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=49967, stack(0x0000000310597000,0x0000000310697000)]
  0x00007f78d0db5000 JavaThread "http-nio-9200-AsyncTimeout" daemon [_thread_blocked, id=58115, stack(0x0000000310494000,0x0000000310594000)]
  0x00007f78d7f6a000 JavaThread "http-nio-9200-Acceptor-0" daemon [_thread_in_native, id=58627, stack(0x0000000310391000,0x0000000310491000)]
  0x00007f78d7f68800 JavaThread "http-nio-9200-ClientPoller-1" daemon [_thread_in_native, id=58883, stack(0x000000031028e000,0x000000031038e000)]
  0x00007f789293f800 JavaThread "http-nio-9200-ClientPoller-0" daemon [_thread_in_native, id=48643, stack(0x000000031018b000,0x000000031028b000)]
  0x00007f78d03cd000 JavaThread "http-nio-9200-exec-10" daemon [_thread_blocked, id=59651, stack(0x0000000310088000,0x0000000310188000)]
  0x00007f789185f000 JavaThread "http-nio-9200-exec-9" daemon [_thread_blocked, id=48131, stack(0x000000030ff85000,0x0000000310085000)]
  0x00007f7892123800 JavaThread "http-nio-9200-exec-8" daemon [_thread_blocked, id=47875, stack(0x000000030fe82000,0x000000030ff82000)]
  0x00007f7892123000 JavaThread "http-nio-9200-exec-7" daemon [_thread_blocked, id=47619, stack(0x000000030fd7f000,0x000000030fe7f000)]
  0x00007f789211d800 JavaThread "http-nio-9200-exec-6" daemon [_thread_blocked, id=47363, stack(0x000000030fc7c000,0x000000030fd7c000)]
  0x00007f78d042b000 JavaThread "http-nio-9200-exec-5" daemon [_thread_blocked, id=60931, stack(0x000000030fb79000,0x000000030fc79000)]
  0x00007f78d04da800 JavaThread "http-nio-9200-exec-4" daemon [_thread_blocked, id=47107, stack(0x000000030fa76000,0x000000030fb76000)]
  0x00007f78920d4800 JavaThread "http-nio-9200-exec-3" daemon [_thread_blocked, id=46851, stack(0x000000030f973000,0x000000030fa73000)]
  0x00007f789295f000 JavaThread "http-nio-9200-exec-2" daemon [_thread_blocked, id=46595, stack(0x000000030f870000,0x000000030f970000)]
  0x00007f78918ad800 JavaThread "http-nio-9200-exec-1" daemon [_thread_blocked, id=46347, stack(0x000000030f76d000,0x000000030f86d000)]
  0x00007f78d0e6d000 JavaThread "NioBlockingSelector.BlockPoller-1" daemon [_thread_in_native, id=45171, stack(0x000000030f66a000,0x000000030f76a000)]
  0x00007f78da46d800 JavaThread "ThreadPoolTaskScheduler-1" [_thread_blocked, id=63787, stack(0x000000030f361000,0x000000030f461000)]
  0x00007f78d7ba3000 JavaThread "com.alibaba.nacos.naming.push.receiver" daemon [_thread_in_native, id=64263, stack(0x000000030f25e000,0x000000030f35e000)]
  0x00007f78d7cf5800 JavaThread "com.alibaba.nacos.naming.failover" daemon [_thread_blocked, id=44547, stack(0x000000030f15b000,0x000000030f25b000)]
  0x00007f78d9c25000 JavaThread "com.alibaba.nacos.naming.client.listener" daemon [_thread_blocked, id=36619, stack(0x000000030f058000,0x000000030f158000)]
  0x00007f78d0b2e000 JavaThread "spring.cloud.inetutils" daemon [_thread_blocked, id=43827, stack(0x000000030ef55000,0x000000030f055000)]
  0x00007f78da52d000 JavaThread "commons-pool-evictor-thread" [_thread_blocked, id=65403, stack(0x000000030ee52000,0x000000030ef52000)]
  0x00007f7890023800 JavaThread "Druid-ConnectionPool-Destroy-217123986" daemon [_thread_blocked, id=41347, stack(0x000000030e331000,0x000000030e431000)]
  0x00007f78d908a000 JavaThread "Druid-ConnectionPool-Create-217123986" daemon [_thread_blocked, id=36367, stack(0x000000030e22e000,0x000000030e32e000)]
  0x00007f78d0400000 JavaThread "Abandoned connection cleanup thread" daemon [_thread_blocked, id=42015, stack(0x000000030e63a000,0x000000030e73a000)]
  0x00007f78d7c78800 JavaThread "container-0" [_thread_blocked, id=34675, stack(0x000000030e537000,0x000000030e637000)]
  0x00007f78d0fa1800 JavaThread "ContainerBackgroundProcessor[StandardEngine[Tomcat]]" daemon [_thread_blocked, id=34183, stack(0x000000030e434000,0x000000030e534000)]
  0x00007f78c7f0f000 JavaThread "Thread-12" daemon [_thread_blocked, id=26703, stack(0x000000030de22000,0x000000030df22000)]
  0x00007f78c7df7800 JavaThread "com.alibaba.nacos.client.Worker.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=26083, stack(0x000000030e028000,0x000000030e128000)]
  0x00007f78c7d11000 JavaThread "Timer-0" daemon [_thread_blocked, id=26171, stack(0x000000030df25000,0x000000030e025000)]
  0x00007f78c7c5e000 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=27723, stack(0x000000030dd1f000,0x000000030de1f000)]
  0x00007f78c7c1e000 JavaThread "Attach Listener" daemon [_thread_blocked, id=24603, stack(0x000000030db19000,0x000000030dc19000)]
  0x00007f78d7a10800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=29999, stack(0x000000030d913000,0x000000030da13000)]
  0x00007f78d01ff800 JavaThread "Service Thread" daemon [_thread_blocked, id=30979, stack(0x000000030d810000,0x000000030d910000)]
  0x00007f78d01fe800 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=31747, stack(0x000000030d70d000,0x000000030d80d000)]
  0x00007f78d022c800 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=22787, stack(0x000000030d60a000,0x000000030d70a000)]
  0x00007f78c7b03800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=22531, stack(0x000000030d507000,0x000000030d607000)]
  0x00007f78c7af8800 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=22287, stack(0x000000030d404000,0x000000030d504000)]
  0x00007f78d0a4b800 JavaThread "Thread-2" daemon [_thread_blocked, id=18243, stack(0x000000030d301000,0x000000030d401000)]
  0x00007f78d9abd800 JavaThread "HotSwap Dispatcher" daemon [_thread_blocked, id=17667, stack(0x000000030d1fe000,0x000000030d2fe000)]
  0x00007f78d9ac3000 JavaThread "HotSwap Watcher" daemon [_thread_blocked, id=17563, stack(0x000000030d0fb000,0x000000030d1fb000)]
  0x00007f78d7917800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=20227, stack(0x000000030cef5000,0x000000030cff5000)]
  0x00007f78c7a02800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=20995, stack(0x000000030ccef000,0x000000030cdef000)]
  0x00007f78d089e000 JavaThread "Finalizer" daemon [_thread_blocked, id=15395, stack(0x000000030cade000,0x000000030cbde000)]
  0x00007f78d00a4800 JavaThread "Reference Handler" daemon [_thread_blocked, id=11811, stack(0x000000030c9db000,0x000000030cadb000)]

Other Threads:
  0x00007f78d98d6000 VMThread [stack: 0x000000030c8d8000,0x000000030c9d8000] [id=11591]
  0x00007f78d7ac4000 WatcherThread [stack: 0x000000030da16000,0x000000030db16000] [id=24295]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap:
 def new generation   total 177024K, used 106908K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,  63% used [0x0000000580000000, 0x00000005861f1b60, 0x00000005899b0000)
  from space 19648K,  33% used [0x00000005899b0000, 0x000000058a025898, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 80363K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  20% used [0x0000000640000000, 0x0000000644e7ac80, 0x0000000644e7ae00, 0x0000000658000000)
 Metaspace       used 80142K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9163K, capacity 9483K, committed 9600K, reserved 1048576K

Card table byte_map: [0x000000011e155000,0x000000011f356000] byte_map_base: 0x000000011b555000

Polling page: 0x000000010c8e7000

CodeCache: size=245760Kb used=19723Kb max_used=19723Kb free=226036Kb
 bounds [0x000000010f155000, 0x00000001104a5000, 0x000000011e155000]
 total_blobs=8754 nmethods=8153 adapters=524
 compilation: enabled

Compilation events (10 events):
Event: 779.953 Thread 0x00007f78d01fe800 8350       1       java.net.URI::access$3100 (4 bytes)
Event: 779.954 Thread 0x00007f78d01fe800 nmethod 8350 0x0000000110499850 code [0x00000001104999a0, 0x0000000110499a90]
Event: 779.958 Thread 0x00007f78d01fe800 8351       1       java.net.URI::access$3200 (4 bytes)
Event: 779.958 Thread 0x00007f78d01fe800 nmethod 8351 0x0000000110499b10 code [0x0000000110499c60, 0x0000000110499d50]
Event: 800.029 Thread 0x00007f78d01fe800 8352       1       java.util.UUID::<init> (109 bytes)
Event: 800.032 Thread 0x00007f78d01fe800 nmethod 8352 0x0000000110499dd0 code [0x0000000110499f40, 0x000000011049a110]
Event: 805.046 Thread 0x00007f78d01fe800 8353       1       java.util.Collections$SynchronizedCollection::<init> (24 bytes)
Event: 805.047 Thread 0x00007f78d01fe800 nmethod 8353 0x000000011049a350 code [0x000000011049a4e0, 0x000000011049a798]
Event: 810.074 Thread 0x00007f78d01fe800 8354       1       sun.net.www.HeaderParser::findValue (66 bytes)
Event: 810.080 Thread 0x00007f78d01fe800 nmethod 8354 0x000000011049a9d0 code [0x000000011049ab60, 0x000000011049ae38]

GC Heap History (10 events):
Event: 25.768 GC heap before
{Heap before GC invocations=27 (full 3):
 def new generation   total 177024K, used 168503K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  56% used [0x000000058ace0000, 0x000000058b7bdce8, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 71474K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  18% used [0x0000000640000000, 0x00000006445ccbf0, 0x00000006445ccc00, 0x0000000658000000)
 Metaspace       used 74675K, capacity 75716K, committed 76160K, reserved 1116160K
  class space    used 8515K, capacity 8784K, committed 8832K, reserved 1048576K
Event: 25.789 GC heap after
Heap after GC invocations=28 (full 3):
 def new generation   total 177024K, used 10248K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  52% used [0x00000005899b0000, 0x000000058a3b21a8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 73614K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  18% used [0x0000000640000000, 0x00000006447e3960, 0x00000006447e3a00, 0x0000000658000000)
 Metaspace       used 74675K, capacity 75716K, committed 76160K, reserved 1116160K
  class space    used 8515K, capacity 8784K, committed 8832K, reserved 1048576K
}
Event: 26.258 GC heap before
{Heap before GC invocations=28 (full 3):
 def new generation   total 177024K, used 167624K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  52% used [0x00000005899b0000, 0x000000058a3b21a8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 73614K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  18% used [0x0000000640000000, 0x00000006447e3960, 0x00000006447e3a00, 0x0000000658000000)
 Metaspace       used 76115K, capacity 77256K, committed 77696K, reserved 1118208K
  class space    used 8718K, capacity 9018K, committed 9088K, reserved 1048576K
Event: 26.283 GC heap after
Heap after GC invocations=29 (full 3):
 def new generation   total 177024K, used 12755K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  64% used [0x000000058ace0000, 0x000000058b954d88, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 75549K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x00000006449c7630, 0x00000006449c7800, 0x0000000658000000)
 Metaspace       used 76115K, capacity 77256K, committed 77696K, reserved 1118208K
  class space    used 8718K, capacity 9018K, committed 9088K, reserved 1048576K
}
Event: 27.077 GC heap before
{Heap before GC invocations=29 (full 3):
 def new generation   total 177024K, used 170131K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  64% used [0x000000058ace0000, 0x000000058b954d88, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 75549K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x00000006449c7630, 0x00000006449c7800, 0x0000000658000000)
 Metaspace       used 78316K, capacity 79530K, committed 79744K, reserved 1120256K
  class space    used 8970K, capacity 9279K, committed 9344K, reserved 1048576K
Event: 27.098 GC heap after
Heap after GC invocations=30 (full 3):
 def new generation   total 177024K, used 9859K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  50% used [0x00000005899b0000, 0x000000058a350f10, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 78370K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c88be0, 0x0000000644c88c00, 0x0000000658000000)
 Metaspace       used 78316K, capacity 79530K, committed 79744K, reserved 1120256K
  class space    used 8970K, capacity 9279K, committed 9344K, reserved 1048576K
}
Event: 254.441 GC heap before
{Heap before GC invocations=30 (full 3):
 def new generation   total 177024K, used 167235K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  50% used [0x00000005899b0000, 0x000000058a350f10, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 78370K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c88be0, 0x0000000644c88c00, 0x0000000658000000)
 Metaspace       used 80140K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9163K, capacity 9483K, committed 9600K, reserved 1048576K
Event: 254.507 GC heap after
Heap after GC invocations=31 (full 3):
 def new generation   total 177024K, used 7800K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  39% used [0x000000058ace0000, 0x000000058b47e220, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 80363K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  20% used [0x0000000640000000, 0x0000000644e7ac80, 0x0000000644e7ae00, 0x0000000658000000)
 Metaspace       used 80140K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9163K, capacity 9483K, committed 9600K, reserved 1048576K
}
Event: 598.510 GC heap before
{Heap before GC invocations=31 (full 3):
 def new generation   total 177024K, used 165176K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  39% used [0x000000058ace0000, 0x000000058b47e220, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 80363K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  20% used [0x0000000640000000, 0x0000000644e7ac80, 0x0000000644e7ae00, 0x0000000658000000)
 Metaspace       used 80140K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9163K, capacity 9483K, committed 9600K, reserved 1048576K
Event: 598.559 GC heap after
Heap after GC invocations=32 (full 3):
 def new generation   total 177024K, used 6614K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  33% used [0x00000005899b0000, 0x000000058a025898, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 80363K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  20% used [0x0000000640000000, 0x0000000644e7ac80, 0x0000000644e7ae00, 0x0000000658000000)
 Metaspace       used 80140K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9163K, capacity 9483K, committed 9600K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Internal exceptions (10 events):
Event: 765.278 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000584575318) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 765.279 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000584575998) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 775.288 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x000000058457ed70) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 775.289 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x000000058457f3f0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 785.293 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000584e85678) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 785.293 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000584e85cf8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 795.287 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000584e8f0d0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 795.288 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000584e8f750) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 805.291 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000585aacae0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 805.291 Thread 0x00007f78c7d11000 Exception <a 'java/io/FileNotFoundException'> (0x0000000585aad160) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]

Events (10 events):
Event: 814.535 Executing VM operation: ChangeBreakpoints done
Event: 814.536 Thread 0x00007f78d9009800 Thread exited: 0x00007f78d9009800
Event: 814.536 Executing VM operation: ChangeBreakpoints
Event: 814.536 Executing VM operation: ChangeBreakpoints done
Event: 814.537 Executing VM operation: ChangeBreakpoints
Event: 814.537 Executing VM operation: ChangeBreakpoints done
Event: 814.545 Thread 0x00007f78d7917000 Thread exited: 0x00007f78d7917000
Event: 814.590 loading class sun/misc/Signal$1
Event: 814.596 loading class sun/misc/Signal$1 done
Event: 814.597 Thread 0x00007f78c0013800 Thread added: 0x00007f78c0013800


Dynamic libraries:
0x000000000e0a8000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x000000000e0a8000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000000e0a8000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000000e0a8000 	/usr/lib/libSystem.B.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x000000000e0a8000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000000e0a8000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000000e0a8000 	/usr/lib/libspindump.dylib
0x000000000e0a8000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000000e0a8000 	/usr/lib/libbsm.0.dylib
0x000000000e0a8000 	/usr/lib/libapp_launch_measurement.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000000e0a8000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x000000000e0a8000 	/usr/lib/liblangid.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000000e0a8000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000000e0a8000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000000e0a8000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000000e0a8000 	/usr/lib/libz.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000000e0a8000 	/usr/lib/libicucore.A.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000000e0a8000 	/usr/lib/libMobileGestalt.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000000e0a8000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x000000000e0a8000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000000e0a8000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000000e0a8000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000000e0a8000 	/usr/lib/libenergytrace.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x000000000e0a8000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000000e0a8000 	/usr/lib/libxml2.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000000e0a8000 	/usr/lib/libobjc.A.dylib
0x000000000e0a8000 	/usr/lib/libc++.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000000e0a8000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000000e0a8000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000000e0a8000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000000e0a8000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x000000000e0a8000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x000000000e0a8000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x000000000e0a8000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x000000000e0a8000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x000000000e0a8000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreImage.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDataDetection.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDispatch.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftIOKit.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftMetal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftOSLog.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObservation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftXPC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_errno.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_math.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_signal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_stdio.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftos.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsimd.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsys_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftunistd.dylib
0x000000000e0a8000 	/usr/lib/libcompression.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000000e0a8000 	/usr/lib/libate.dylib
0x000000000e0a8000 	/usr/lib/system/libcache.dylib
0x000000000e0a8000 	/usr/lib/system/libcommonCrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libcompiler_rt.dylib
0x000000000e0a8000 	/usr/lib/system/libcopyfile.dylib
0x000000000e0a8000 	/usr/lib/system/libcorecrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libdispatch.dylib
0x000000000e0a8000 	/usr/lib/system/libdyld.dylib
0x000000000e0a8000 	/usr/lib/system/libkeymgr.dylib
0x000000000e0a8000 	/usr/lib/system/libmacho.dylib
0x000000000e0a8000 	/usr/lib/system/libquarantine.dylib
0x000000000e0a8000 	/usr/lib/system/libremovefile.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_asl.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_blocks.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_c.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_collections.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_configuration.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwin.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_info.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_m.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_malloc.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_notify.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sandbox.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_secinit.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_kernel.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_platform.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_pthread.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_trace.dylib
0x000000000e0a8000 	/usr/lib/system/libunwind.dylib
0x000000000e0a8000 	/usr/lib/system/libxpc.dylib
0x000000000e0a8000 	/usr/lib/libc++abi.dylib
0x000000000e0a8000 	/usr/lib/libRosetta.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000000e0a8000 	/usr/lib/liboah.dylib
0x000000000e0a8000 	/usr/lib/libfakelink.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x000000000e0a8000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x000000000e0a8000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000000e0a8000 	/usr/lib/libapple_nghttp2.dylib
0x000000000e0a8000 	/usr/lib/libsqlite3.dylib
0x000000000e0a8000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000000e0a8000 	/usr/lib/system/libkxld.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x000000000e0a8000 	/usr/lib/libCoreEntitlements.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000000e0a8000 	/usr/lib/libcoretls.dylib
0x000000000e0a8000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000000e0a8000 	/usr/lib/libpam.2.dylib
0x000000000e0a8000 	/usr/lib/libxar.1.dylib
0x000000000e0a8000 	/usr/lib/libarchive.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftSystem.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x000000000e0a8000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x000000000e0a8000 	/usr/lib/libboringssl.dylib
0x000000000e0a8000 	/usr/lib/libdns_services.dylib
0x000000000e0a8000 	/usr/lib/libquic.dylib
0x000000000e0a8000 	/usr/lib/libusrtcp.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x000000000e0a8000 	/usr/lib/swift/libswiftDistributed.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSynchronization.dylib
0x000000000e0a8000 	/usr/lib/libnetwork.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000000e0a8000 	/usr/lib/libAppleArchive.dylib
0x000000000e0a8000 	/usr/lib/libbz2.1.0.dylib
0x000000000e0a8000 	/usr/lib/liblzma.5.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000000e0a8000 	/usr/lib/libgermantok.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000000e0a8000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000000e0a8000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000000e0a8000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x000000000e0a8000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000000e0a8000 	/usr/lib/libutil.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x000000000e0a8000 	/usr/lib/libhvf.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_RegexParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000000e0a8000 	/usr/lib/libexpat.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x000000000e0a8000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000000e0a8000 	/usr/lib/libcups.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000000e0a8000 	/usr/lib/libresolv.9.dylib
0x000000000e0a8000 	/usr/lib/libiconv.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000000e0a8000 	/usr/lib/libheimdal-asn1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000000e0a8000 	/usr/lib/libcharset.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000000e0a8000 	/usr/lib/libAudioStatistics.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x000000000e0a8000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000000e0a8000 	/usr/lib/libSMC.dylib
0x000000000e0a8000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000000e0a8000 	/usr/lib/libperfcheck.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x000000000e0a8000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x000000000e0a8000 	/usr/lib/libmis.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x000000000e0a8000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000000e0a8000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000000e0a8000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000000e0a8000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x000000000e0a8000 	/usr/lib/libAccessibility.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000000e0a8000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x000000000e0a8000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000000e0a8000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000000e0a8000 	/usr/lib/libIOReport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000000e0a8000 	/usr/lib/libTLE.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000000e0a8000 	/usr/lib/libmecab.dylib
0x000000000e0a8000 	/usr/lib/libCRFSuite.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000000e0a8000 	/usr/lib/libThaiTokenizer.dylib
0x000000000e0a8000 	/usr/lib/libCheckFix.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x000000000e0a8000 	/usr/lib/libxslt.1.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x000000000e0a8000 	/usr/lib/libcurl.4.dylib
0x000000000e0a8000 	/usr/lib/libcrypto.46.dylib
0x000000000e0a8000 	/usr/lib/libssl.48.dylib
0x000000000e0a8000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000000e0a8000 	/usr/lib/libsasl2.2.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x000000010d30c000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib
0x000000000e0a8000 	/usr/lib/libstdc++.6.dylib
0x000000010c8f1000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libverify.dylib
0x000000010c93f000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjava.dylib
0x000000010c9e8000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjdwp.dylib
0x000000010c911000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnpt.dylib
0x000000010cbb3000 	/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x000000010ca7d000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libinstrument.dylib
0x000000010c9b2000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libzip.dylib
0x000000010cb9e000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libdt_socket.dylib
0x000000012340d000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnio.dylib
0x0000000123449000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnet.dylib
0x00000001234a6000 	/Users/<USER>/.debugTools/lib/DebugToolsJniLibrary.dylib
0x00000001237e6000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libmanagement.dylib
0x00000001282f2000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libsunec.dylib

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:50092,suspend=y,server=n -agentpath:/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/master_2025_09_05_112323.jfr,log=/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/master_2025_09_05_112323.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dsocket.server.port=33833 -javaagent:/Users/<USER>/.debugTools/lib/debug-tools-agent-4.3.1.jar=server=false,printSql=Pretty,traceSql=false,hotswap=true,autoAttach=false,autoSaveSql=false,sqlRetentionDays=1 -Dfile.encoding=UTF-8 
java_command: com.pes.jd.application.UsrmApplication
java_class_path (initial): /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/charsets.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/cldrdata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/dnsns.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/jaccess.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/localedata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/nashorn.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunec.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/zipfs.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jce.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jfr.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jsse.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/management-agent.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/resources.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/rt.jar:/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-master/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.17.RELEASE/spring-boot-starter-web-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.17.RELEASE/spring-boot-starter-tomcat-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repositor
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/Users/<USER>/.nvm/versions/node/v24.5.0/bin:/opt/homebrew/Cellar/node/24.1.0/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/.orbstack/bin
SHELL=/bin/zsh

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x38958c], sa_mask[0]=00000000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 24.6.0 Darwin Kernel Version 24.6.0: Mon Jul 14 11:28:30 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T6030 x86_64
rlimit: STACK 8176k, CORE 0k, NPROC 6000, NOFILE 10240, AS infinity
load average:2.72 3.36 3.46

CPU:total 12 (initial active 12) (1 cores per cpu, 1 threads per core) family 6 model 44 stepping 0, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, aes, clmul, tsc, tscinvbit, tscinv

Memory: 4k page, physical 37748736k(21008k free)

/proc/meminfo:


vm_info: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26) for bsd-amd64 JRE (1.8.0), built on Jul 31 2018 03:28:48 by "jenkins" with gcc 4.8.2

time: Fri Sep  5 11:36:59 2025
elapsed time: 814 seconds (0d 0h 13m 34s)

