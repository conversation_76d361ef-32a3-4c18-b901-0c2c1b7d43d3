#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x0000000000000000, pid=29429, tid=0x0000000000015147
#
# JRE version: OpenJDK Runtime Environment (8.0) (build 1.8.0_282b08-internal-202101310908-dcevm8u282b08)
# Java VM: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26 mixed mode bsd-amd64 compressed oops)
# Problematic frame:
# C  0x0000000000000000
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://github.com/dcevm/dcevm/issues
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  T H R E A D  ---------------

Current thread (0x00007f9ca738d000):  JavaThread "SIGINT handler" daemon [_thread_in_native, id=86343, stack(0x000000030def8000,0x000000030dff8000)]

siginfo: si_signo: 11 (SIGSEGV), si_code: 1 (SEGV_MAPERR), si_addr: 0x0000000000000000

Registers:
RAX=0x0000000000000000, RBX=0x0000000123cbc7d8, RCX=0x0000000000000028, RDX=0x0000000000000000
RSP=0x000000030dff78b8, RBP=0x000000030dff7908, RSI=0x000000030dff7918, RDI=0x00007f9ca738d1e0
R8 =0x0000600007cbdc20, R9 =0x0000000123cbc7d8, R10=0x000000010f86e3f8, R11=0x000000010dddfef2
R12=0x0000000000000000, R13=0x0000000123cbc7d8, R14=0x000000030dff7920, R15=0x00007f9ca738d000
RIP=0x0000000000000000, EFLAGS=0x0000000000000287, ERR=0x0000000000000004
  TRAPNO=0x000000000000000e

Top of Stack: (sp=0x000000030dff78b8)
0x000000030dff78b8:   000000010f86e424 000000010f86dea2
0x000000030dff78c8:   000000030dff78c8 0000000123cbc7d8
0x000000030dff78d8:   000000030dff7920 0000000123cbcf40
0x000000030dff78e8:   0000000000000000 0000000123cbc7d8
0x000000030dff78f8:   0000000000000000 000000030dff7928
0x000000030dff7908:   000000030dff7978 000000010f85707d
0x000000030dff7918:   000000064080da98 000000010f8651b8
0x000000030dff7928:   0000000000000001 000000064080da98
0x000000030dff7938:   000000030dff7928 0000000123cbccb7
0x000000030dff7948:   000000030dff79a8 0000000123cbcf40
0x000000030dff7958:   0000000000000000 000000012082a410
0x000000030dff7968:   000000030dff7928 000000030dff79a8
0x000000030dff7978:   000000030dff79f0 000000010f85707d
0x000000030dff7988:   0000000000000000 0000000000000000
0x000000030dff7998:   000000064080da98 0000000000000000
0x000000030dff79a8:   0000000000000082 000000030dff79b0
0x000000030dff79b8:   00000001207dfcb0 000000030dff7a08
0x000000030dff79c8:   00000001207dfdc8 0000000000000000
0x000000030dff79d8:   00000001207dfcc0 000000030dff79a8
0x000000030dff79e8:   000000030dff7a00 000000030dff7a50
0x000000030dff79f8:   000000010f8570c2 0000000640965168
0x000000030dff7a08:   0000000640869608 000000030dff7a10
0x000000030dff7a18:   000000012a9cb298 000000030dff7a60
0x000000030dff7a28:   000000012a9cb340 0000000000000000
0x000000030dff7a38:   000000012a9cb2a8 000000030dff7a00
0x000000030dff7a48:   000000030dff7a60 000000030dff7aa8
0x000000030dff7a58:   000000010f8570c2 00000005800114a8
0x000000030dff7a68:   000000030dff7a68 0000000120681bab
0x000000030dff7a78:   000000030dff7ab8 00000001207143f8
0x000000030dff7a88:   0000000000000000 0000000120681bb8
0x000000030dff7a98:   000000030dff7a60 000000030dff7ab8
0x000000030dff7aa8:   000000030dff7b20 000000010f84f4e7 

Instructions: (pc=0x0000000000000000)
0xffffffffffffffe0:   

Register to memory mapping:

RAX=0x0000000000000000 is an unknown value
RBX={method} {0x0000000123cbc7d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
RCX=0x0000000000000028 is an unknown value
RDX=0x0000000000000000 is an unknown value
RSP=0x000000030dff78b8 is pointing into the stack for thread: 0x00007f9ca738d000
RBP=0x000000030dff7908 is pointing into the stack for thread: 0x00007f9ca738d000
RSI=0x000000030dff7918 is pointing into the stack for thread: 0x00007f9ca738d000
RDI=0x00007f9ca738d1e0 is an unknown value
R8 =0x0000600007cbdc20 is an unknown value
R9 ={method} {0x0000000123cbc7d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R10=0x000000010f86e3f8 is at code_begin+1528 in an Interpreter codelet
method entry point (kind = native)  [0x000000010f86de00, 0x000000010f86ed20]  3872 bytes
R11=0x000000010dddfef2: throw_unsatisfied_link_error+0 in /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib at 0x000000010da06000
R12=0x0000000000000000 is an unknown value
R13={method} {0x0000000123cbc7d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R14=0x000000030dff7920 is pointing into the stack for thread: 0x00007f9ca738d000
R15=0x00007f9ca738d000 is a thread


Stack: [0x000000030def8000,0x000000030dff8000],  sp=0x000000030dff78b8,  free space=1022k
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.Shutdown.beforeHalt()V+0
j  java.lang.Shutdown.exit(I)V+95
j  java.lang.Terminator$1.handle(Lsun/misc/Signal;)V+8
j  sun.misc.Signal$1.run()V+8
j  java.lang.Thread.run()V+11
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
=>0x00007f9ca738d000 JavaThread "SIGINT handler" daemon [_thread_in_native, id=86343, stack(0x000000030def8000,0x000000030dff8000)]
  0x00007f9c1605d000 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=72479, stack(0x000000030dcf2000,0x000000030ddf2000)]
  0x00007f9c35057800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=25095, stack(0x00000003119a6000,0x0000000311aa6000)]
  0x00007f9c15864800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=35155, stack(0x0000000310467000,0x0000000310567000)]
  0x00007f9c64852800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_in_native, id=41159, stack(0x000000030fd52000,0x000000030fe52000)]
  0x00007f9c1482d800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=37407, stack(0x000000030f437000,0x000000030f537000)]
  0x00007f9c35050800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=41659, stack(0x000000030f946000,0x000000030fa46000)]
  0x00007f9c35054000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=53847, stack(0x000000030f843000,0x000000030f943000)]
  0x00007f9c64851000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=53535, stack(0x000000030f231000,0x000000030f331000)]
  0x00007f9c6485d800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=57091, stack(0x000000030ed22000,0x000000030ee22000)]
  0x00007f9c3505f800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=44559, stack(0x000000030fa49000,0x000000030fb49000)]
  0x00007f9c6485a800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=43311, stack(0x000000030fb4c000,0x000000030fc4c000)]
  0x00007f9c15861800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=36135, stack(0x0000000311db2000,0x0000000311eb2000)]
  0x00007f9c17261800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=56447, stack(0x0000000311aa9000,0x0000000311ba9000)]
  0x00007f9ca6ba5000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=42243, stack(0x000000031159a000,0x000000031169a000)]
  0x00007f9c94d36000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=53299, stack(0x00000003118a3000,0x00000003119a3000)]
  0x00007f9ca64bb000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=56187, stack(0x000000030fc4f000,0x000000030fd4f000)]
  0x00007f9ca6703000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=57603, stack(0x00000003117a0000,0x00000003118a0000)]
  0x00007f9c17262800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=51543, stack(0x000000031169d000,0x000000031179d000)]
  0x00007f9ca597e000 JavaThread "DestroyJavaVM" [_thread_blocked, id=6403, stack(0x000000030d6d4000,0x000000030d7d4000)]
  0x00007f9ca75e6800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=50455, stack(0x0000000311497000,0x0000000311597000)]
  0x00007f9ca5c34000 JavaThread "http-nio-9200-AsyncTimeout" daemon [_thread_blocked, id=58883, stack(0x0000000311394000,0x0000000311494000)]
  0x00007f9ca5d37800 JavaThread "http-nio-9200-Acceptor-0" daemon [_thread_in_native, id=49667, stack(0x0000000311291000,0x0000000311391000)]
  0x00007f9ca76c6800 JavaThread "http-nio-9200-ClientPoller-1" daemon [_thread_in_native, id=49411, stack(0x000000031118e000,0x000000031128e000)]
  0x00007f9ca7370000 JavaThread "http-nio-9200-ClientPoller-0" daemon [_thread_in_native, id=48899, stack(0x000000031108b000,0x000000031118b000)]
  0x00007f9ca751f800 JavaThread "http-nio-9200-exec-10" daemon [_thread_blocked, id=48391, stack(0x0000000310f88000,0x0000000311088000)]
  0x00007f9ca76bc000 JavaThread "http-nio-9200-exec-9" daemon [_thread_blocked, id=60419, stack(0x0000000310e85000,0x0000000310f85000)]
  0x00007f9c94dc6800 JavaThread "http-nio-9200-exec-8" daemon [_thread_blocked, id=48131, stack(0x0000000310d82000,0x0000000310e82000)]
  0x00007f9c94d8e000 JavaThread "http-nio-9200-exec-7" daemon [_thread_blocked, id=60931, stack(0x0000000310c7f000,0x0000000310d7f000)]
  0x00007f9ca6a8d000 JavaThread "http-nio-9200-exec-6" daemon [_thread_blocked, id=47619, stack(0x0000000310b7c000,0x0000000310c7c000)]
  0x00007f9c17a7c000 JavaThread "http-nio-9200-exec-5" daemon [_thread_blocked, id=61699, stack(0x0000000310a79000,0x0000000310b79000)]
  0x00007f9c17a7b800 JavaThread "http-nio-9200-exec-4" daemon [_thread_blocked, id=47107, stack(0x0000000310976000,0x0000000310a76000)]
  0x00007f9ca66b8800 JavaThread "http-nio-9200-exec-3" daemon [_thread_blocked, id=46851, stack(0x0000000310873000,0x0000000310973000)]
  0x00007f9ca629e000 JavaThread "http-nio-9200-exec-2" daemon [_thread_blocked, id=62467, stack(0x0000000310770000,0x0000000310870000)]
  0x00007f9ca6618800 JavaThread "http-nio-9200-exec-1" daemon [_thread_blocked, id=46603, stack(0x000000031066d000,0x000000031076d000)]
  0x00007f9ca6675000 JavaThread "NioBlockingSelector.BlockPoller-1" daemon [_thread_in_native, id=46251, stack(0x000000031056a000,0x000000031066a000)]
  0x00007f9c94db1000 JavaThread "ThreadPoolTaskScheduler-1" [_thread_blocked, id=63851, stack(0x0000000310364000,0x0000000310464000)]
  0x00007f9ca7497800 JavaThread "com.alibaba.nacos.naming.push.receiver" daemon [_thread_in_native, id=64519, stack(0x0000000310261000,0x0000000310361000)]
  0x00007f9c94d87800 JavaThread "com.alibaba.nacos.naming.failover" daemon [_thread_blocked, id=65027, stack(0x000000031015e000,0x000000031025e000)]
  0x00007f9c94c31800 JavaThread "com.alibaba.nacos.naming.client.listener" daemon [_thread_blocked, id=37151, stack(0x000000031005b000,0x000000031015b000)]
  0x00007f9c1685c800 JavaThread "spring.cloud.inetutils" daemon [_thread_blocked, id=36731, stack(0x000000030ff58000,0x0000000310058000)]
  0x00007f9c94b4f000 JavaThread "commons-pool-evictor-thread" [_thread_blocked, id=43639, stack(0x000000030fe55000,0x000000030ff55000)]
  0x00007f9c16049000 JavaThread "Druid-ConnectionPool-Destroy-217123986" daemon [_thread_blocked, id=33795, stack(0x000000030f334000,0x000000030f434000)]
  0x00007f9c15866000 JavaThread "Druid-ConnectionPool-Create-217123986" daemon [_thread_blocked, id=38771, stack(0x000000030f12e000,0x000000030f22e000)]
  0x00007f9ca6398000 JavaThread "Abandoned connection cleanup thread" daemon [_thread_blocked, id=39483, stack(0x000000030f740000,0x000000030f840000)]
  0x00007f9ca5b72000 JavaThread "container-0" [_thread_blocked, id=40007, stack(0x000000030f63d000,0x000000030f73d000)]
  0x00007f9ca6b07000 JavaThread "ContainerBackgroundProcessor[StandardEngine[Tomcat]]" daemon [_thread_blocked, id=40707, stack(0x000000030f53a000,0x000000030f63a000)]
  0x00007f9ca64eb000 JavaThread "Thread-14" daemon [_thread_blocked, id=42535, stack(0x000000030ee25000,0x000000030ef25000)]
  0x00007f9c16876000 JavaThread "com.alibaba.nacos.client.Worker.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=33035, stack(0x000000030f02b000,0x000000030f12b000)]
  0x00007f9ca5c19000 JavaThread "Timer-0" daemon [_thread_blocked, id=12495, stack(0x000000030ef28000,0x000000030f028000)]
  0x00007f9ca7240000 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=26207, stack(0x000000030ec1f000,0x000000030ed1f000)]
  0x00007f9ca4a6c000 JavaThread "Attach Listener" daemon [_thread_blocked, id=27651, stack(0x000000030ea19000,0x000000030eb19000)]
  0x00007f9c94aa6000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=29187, stack(0x000000030e813000,0x000000030e913000)]
  0x00007f9c34835000 JavaThread "Service Thread" daemon [_thread_blocked, id=22531, stack(0x000000030e710000,0x000000030e810000)]
  0x00007f9ca6a5b800 JavaThread "C1 CompilerThread3" daemon [_thread_in_vm, id=30979, stack(0x000000030e60d000,0x000000030e70d000)]
  0x00007f9ca6a5b000 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=22019, stack(0x000000030e50a000,0x000000030e60a000)]
  0x00007f9c35008800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=31747, stack(0x000000030e407000,0x000000030e507000)]
  0x00007f9ca6a19000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=32279, stack(0x000000030e304000,0x000000030e404000)]
  0x00007f9ca5af2800 JavaThread "Thread-2" daemon [_thread_blocked, id=19719, stack(0x000000030e201000,0x000000030e301000)]
  0x00007f9ca4a61800 JavaThread "HotSwap Dispatcher" daemon [_thread_blocked, id=19971, stack(0x000000030e0fe000,0x000000030e1fe000)]
  0x00007f9ca634a000 JavaThread "HotSwap Watcher" daemon [_thread_blocked, id=20507, stack(0x000000030dffb000,0x000000030e0fb000)]
  0x00007f9ca6135000 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=20739, stack(0x000000030ddf5000,0x000000030def5000)]
  0x00007f9ca7011800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=16899, stack(0x000000030dbef000,0x000000030dcef000)]
  0x00007f9ca600b000 JavaThread "Finalizer" daemon [_thread_blocked, id=11551, stack(0x000000030d9dd000,0x000000030dadd000)]
  0x00007f9c948b1000 JavaThread "Reference Handler" daemon [_thread_blocked, id=14883, stack(0x000000030d8da000,0x000000030d9da000)]

Other Threads:
  0x00007f9ca48f3800 VMThread [stack: 0x000000030d7d7000,0x000000030d8d7000] [id=15371]
  0x00007f9ca4b22000 WatcherThread [stack: 0x000000030e916000,0x000000030ea16000] [id=28775]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap:
 def new generation   total 177024K, used 4918K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   1% used [0x0000000580000000, 0x000000058019fba0, 0x00000005899b0000)
  from space 19648K,  16% used [0x000000058ace0000, 0x000000058b00dfa0, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 86190K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x000000064542bba8, 0x000000064542bc00, 0x0000000658000000)
 Metaspace       used 83910K, capacity 85340K, committed 85504K, reserved 1124352K
  class space    used 9548K, capacity 9880K, committed 9984K, reserved 1048576K

Card table byte_map: [0x000000011e84f000,0x000000011fa50000] byte_map_base: 0x000000011bc4f000

Polling page: 0x000000010cfe1000

CodeCache: size=245760Kb used=24686Kb max_used=24686Kb free=221073Kb
 bounds [0x000000010f84f000, 0x000000011107f000, 0x000000011e84f000]
 total_blobs=11138 nmethods=10526 adapters=534
 compilation: enabled

Compilation events (10 events):
Event: 5646.503 Thread 0x00007f9ca6a5b800 10777       1       java.nio.charset.CharsetEncoder::onUnmappableCharacter (26 bytes)
Event: 5646.503 Thread 0x00007f9ca6a5b800 nmethod 10777 0x000000011106dc10 code [0x000000011106dd80, 0x000000011106df68]
Event: 5646.504 Thread 0x00007f9ca6a5b800 10778       1       java.nio.charset.CharsetEncoder::implOnUnmappableCharacter (1 bytes)
Event: 5646.504 Thread 0x00007f9ca6a5b800 nmethod 10778 0x000000011106e0d0 code [0x000000011106e220, 0x000000011106e330]
Event: 5646.505 Thread 0x00007f9ca6a5b800 10779       1       java.nio.charset.CharsetEncoder::onMalformedInput (26 bytes)
Event: 5646.505 Thread 0x00007f9ca6a5b800 nmethod 10779 0x000000011106e390 code [0x000000011106e500, 0x000000011106e6e8]
Event: 5646.508 Thread 0x00007f9ca6a5b800 10780       1       java.nio.charset.CharsetEncoder::implOnMalformedInput (1 bytes)
Event: 5646.508 Thread 0x00007f9ca6a5b800 nmethod 10780 0x000000011106e850 code [0x000000011106e9a0, 0x000000011106eab0]
Event: 5646.537 Thread 0x00007f9ca6a5b800 10789       1       java.lang.ref.Reference::clear (6 bytes)
Event: 5646.537 Thread 0x00007f9ca6a5b800 nmethod 10789 0x000000011106eb10 code [0x000000011106ec60, 0x000000011106ed90]

GC Heap History (10 events):
Event: 4496.469 GC heap before
{Heap before GC invocations=42 (full 3):
 def new generation   total 177024K, used 164065K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  34% used [0x00000005899b0000, 0x000000058a038540, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 79626K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  20% used [0x0000000640000000, 0x0000000644dc2908, 0x0000000644dc2a00, 0x0000000658000000)
 Metaspace       used 80844K, capacity 82138K, committed 82560K, reserved 1122304K
  class space    used 9235K, capacity 9579K, committed 9600K, reserved 1048576K
Event: 4496.506 GC heap after
Heap after GC invocations=43 (full 3):
 def new generation   total 177024K, used 5919K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  30% used [0x000000058ace0000, 0x000000058b2a7cc0, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 80850K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  20% used [0x0000000640000000, 0x0000000644ef4b28, 0x0000000644ef4c00, 0x0000000658000000)
 Metaspace       used 80844K, capacity 82138K, committed 82560K, reserved 1122304K
  class space    used 9235K, capacity 9579K, committed 9600K, reserved 1048576K
}
Event: 4717.769 GC heap before
{Heap before GC invocations=43 (full 3):
 def new generation   total 177024K, used 163295K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  30% used [0x000000058ace0000, 0x000000058b2a7cc0, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 80850K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  20% used [0x0000000640000000, 0x0000000644ef4b28, 0x0000000644ef4c00, 0x0000000658000000)
 Metaspace       used 82824K, capacity 84108K, committed 84608K, reserved 1124352K
  class space    used 9437K, capacity 9742K, committed 9856K, reserved 1048576K
Event: 4717.824 GC heap after
Heap after GC invocations=44 (full 3):
 def new generation   total 177024K, used 4767K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  24% used [0x00000005899b0000, 0x0000000589e57f40, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 84106K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x0000000645222838, 0x0000000645222a00, 0x0000000658000000)
 Metaspace       used 82824K, capacity 84108K, committed 84608K, reserved 1124352K
  class space    used 9437K, capacity 9742K, committed 9856K, reserved 1048576K
}
Event: 5066.867 GC heap before
{Heap before GC invocations=44 (full 3):
 def new generation   total 177024K, used 162143K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  24% used [0x00000005899b0000, 0x0000000589e57f40, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 84106K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x0000000645222838, 0x0000000645222a00, 0x0000000658000000)
 Metaspace       used 83057K, capacity 84332K, committed 84864K, reserved 1124352K
  class space    used 9469K, capacity 9774K, committed 9856K, reserved 1048576K
Event: 5066.911 GC heap after
Heap after GC invocations=45 (full 3):
 def new generation   total 177024K, used 2506K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  12% used [0x000000058ace0000, 0x000000058af52bf0, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 85618K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x000000064539ca50, 0x000000064539cc00, 0x0000000658000000)
 Metaspace       used 83057K, capacity 84332K, committed 84864K, reserved 1124352K
  class space    used 9469K, capacity 9774K, committed 9856K, reserved 1048576K
}
Event: 5428.324 GC heap before
{Heap before GC invocations=45 (full 3):
 def new generation   total 177024K, used 159882K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  12% used [0x000000058ace0000, 0x000000058af52bf0, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 85618K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x000000064539ca50, 0x000000064539cc00, 0x0000000658000000)
 Metaspace       used 83062K, capacity 84332K, committed 84864K, reserved 1124352K
  class space    used 9469K, capacity 9774K, committed 9856K, reserved 1048576K
Event: 5428.362 GC heap after
Heap after GC invocations=46 (full 3):
 def new generation   total 177024K, used 1765K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,   8% used [0x00000005899b0000, 0x0000000589b695d8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 86183K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x0000000645429ed0, 0x000000064542a000, 0x0000000658000000)
 Metaspace       used 83062K, capacity 84332K, committed 84864K, reserved 1124352K
  class space    used 9469K, capacity 9774K, committed 9856K, reserved 1048576K
}
Event: 5646.512 GC heap before
{Heap before GC invocations=46 (full 3):
 def new generation   total 177024K, used 159141K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,   8% used [0x00000005899b0000, 0x0000000589b695d8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 86183K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x0000000645429ed0, 0x000000064542a000, 0x0000000658000000)
 Metaspace       used 83908K, capacity 85340K, committed 85504K, reserved 1124352K
  class space    used 9547K, capacity 9880K, committed 9984K, reserved 1048576K
Event: 5646.528 GC heap after
Heap after GC invocations=47 (full 3):
 def new generation   total 177024K, used 3255K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  16% used [0x000000058ace0000, 0x000000058b00dfa0, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 86190K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  21% used [0x0000000640000000, 0x000000064542bba8, 0x000000064542bc00, 0x0000000658000000)
 Metaspace       used 83908K, capacity 85340K, committed 85504K, reserved 1124352K
  class space    used 9547K, capacity 9880K, committed 9984K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Internal exceptions (10 events):
Event: 5631.819 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x0000000587c61b10) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5631.973 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x00000005885afae0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5631.986 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x00000005888c1f78) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5631.994 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x00000005889d64c8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5631.995 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x0000000588a0aae0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5631.995 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x0000000588a106e8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5631.996 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x0000000588a2def8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5631.996 Thread 0x00007f9c172fb000 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x0000000588a5fd80) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/runtime/sharedRuntime.cpp, lin
Event: 5639.162 Thread 0x00007f9ca5c19000 Exception <a 'java/io/FileNotFoundException'> (0x0000000588fdde70) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 5639.162 Thread 0x00007f9ca5c19000 Exception <a 'java/io/FileNotFoundException'> (0x0000000588fde4f0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]

Events (10 events):
Event: 5646.537 Executing VM operation: BulkRevokeBias
Event: 5646.537 Executing VM operation: BulkRevokeBias done
Event: 5646.538 Executing VM operation: BulkRevokeBias
Event: 5646.538 Executing VM operation: BulkRevokeBias done
Event: 5646.540 Thread 0x00007f9c17261800 DEOPT PACKING pc=0x0000000110ba629c sp=0x0000000311ba8660
Event: 5646.540 Thread 0x00007f9c17261800 DEOPT UNPACKING pc=0x000000010f895f73 sp=0x0000000311ba83d0 mode 0
Event: 5646.540 Thread 0x00007f9c17261800 DEOPT PACKING pc=0x000000011071140c sp=0x0000000311ba8740
Event: 5646.540 Thread 0x00007f9c17261800 DEOPT UNPACKING pc=0x000000010f895f73 sp=0x0000000311ba8510 mode 0
Event: 5646.541 loading class sun/misc/Signal$1 done
Event: 5646.542 Thread 0x00007f9ca738d000 Thread added: 0x00007f9ca738d000


Dynamic libraries:
0x000000000e0a8000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x000000000e0a8000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000000e0a8000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000000e0a8000 	/usr/lib/libSystem.B.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x000000000e0a8000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000000e0a8000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000000e0a8000 	/usr/lib/libspindump.dylib
0x000000000e0a8000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000000e0a8000 	/usr/lib/libbsm.0.dylib
0x000000000e0a8000 	/usr/lib/libapp_launch_measurement.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000000e0a8000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x000000000e0a8000 	/usr/lib/liblangid.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000000e0a8000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000000e0a8000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000000e0a8000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000000e0a8000 	/usr/lib/libz.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000000e0a8000 	/usr/lib/libicucore.A.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000000e0a8000 	/usr/lib/libMobileGestalt.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000000e0a8000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x000000000e0a8000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000000e0a8000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000000e0a8000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000000e0a8000 	/usr/lib/libenergytrace.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x000000000e0a8000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000000e0a8000 	/usr/lib/libxml2.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000000e0a8000 	/usr/lib/libobjc.A.dylib
0x000000000e0a8000 	/usr/lib/libc++.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000000e0a8000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000000e0a8000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000000e0a8000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000000e0a8000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x000000000e0a8000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x000000000e0a8000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x000000000e0a8000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x000000000e0a8000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x000000000e0a8000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreImage.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDataDetection.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDispatch.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftIOKit.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftMetal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftOSLog.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObservation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftXPC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_errno.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_math.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_signal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_stdio.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftos.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsimd.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsys_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftunistd.dylib
0x000000000e0a8000 	/usr/lib/libcompression.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000000e0a8000 	/usr/lib/libate.dylib
0x000000000e0a8000 	/usr/lib/system/libcache.dylib
0x000000000e0a8000 	/usr/lib/system/libcommonCrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libcompiler_rt.dylib
0x000000000e0a8000 	/usr/lib/system/libcopyfile.dylib
0x000000000e0a8000 	/usr/lib/system/libcorecrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libdispatch.dylib
0x000000000e0a8000 	/usr/lib/system/libdyld.dylib
0x000000000e0a8000 	/usr/lib/system/libkeymgr.dylib
0x000000000e0a8000 	/usr/lib/system/libmacho.dylib
0x000000000e0a8000 	/usr/lib/system/libquarantine.dylib
0x000000000e0a8000 	/usr/lib/system/libremovefile.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_asl.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_blocks.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_c.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_collections.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_configuration.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwin.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_info.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_m.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_malloc.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_notify.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sandbox.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_secinit.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_kernel.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_platform.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_pthread.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_trace.dylib
0x000000000e0a8000 	/usr/lib/system/libunwind.dylib
0x000000000e0a8000 	/usr/lib/system/libxpc.dylib
0x000000000e0a8000 	/usr/lib/libc++abi.dylib
0x000000000e0a8000 	/usr/lib/libRosetta.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000000e0a8000 	/usr/lib/liboah.dylib
0x000000000e0a8000 	/usr/lib/libfakelink.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x000000000e0a8000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x000000000e0a8000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000000e0a8000 	/usr/lib/libapple_nghttp2.dylib
0x000000000e0a8000 	/usr/lib/libsqlite3.dylib
0x000000000e0a8000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000000e0a8000 	/usr/lib/system/libkxld.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x000000000e0a8000 	/usr/lib/libCoreEntitlements.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000000e0a8000 	/usr/lib/libcoretls.dylib
0x000000000e0a8000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000000e0a8000 	/usr/lib/libpam.2.dylib
0x000000000e0a8000 	/usr/lib/libxar.1.dylib
0x000000000e0a8000 	/usr/lib/libarchive.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftSystem.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x000000000e0a8000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x000000000e0a8000 	/usr/lib/libboringssl.dylib
0x000000000e0a8000 	/usr/lib/libdns_services.dylib
0x000000000e0a8000 	/usr/lib/libquic.dylib
0x000000000e0a8000 	/usr/lib/libusrtcp.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x000000000e0a8000 	/usr/lib/swift/libswiftDistributed.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSynchronization.dylib
0x000000000e0a8000 	/usr/lib/libnetwork.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000000e0a8000 	/usr/lib/libAppleArchive.dylib
0x000000000e0a8000 	/usr/lib/libbz2.1.0.dylib
0x000000000e0a8000 	/usr/lib/liblzma.5.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000000e0a8000 	/usr/lib/libgermantok.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000000e0a8000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000000e0a8000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000000e0a8000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x000000000e0a8000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000000e0a8000 	/usr/lib/libutil.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x000000000e0a8000 	/usr/lib/libhvf.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_RegexParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000000e0a8000 	/usr/lib/libexpat.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x000000000e0a8000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000000e0a8000 	/usr/lib/libcups.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000000e0a8000 	/usr/lib/libresolv.9.dylib
0x000000000e0a8000 	/usr/lib/libiconv.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000000e0a8000 	/usr/lib/libheimdal-asn1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000000e0a8000 	/usr/lib/libcharset.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000000e0a8000 	/usr/lib/libAudioStatistics.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x000000000e0a8000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000000e0a8000 	/usr/lib/libSMC.dylib
0x000000000e0a8000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000000e0a8000 	/usr/lib/libperfcheck.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x000000000e0a8000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x000000000e0a8000 	/usr/lib/libmis.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x000000000e0a8000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000000e0a8000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000000e0a8000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000000e0a8000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x000000000e0a8000 	/usr/lib/libAccessibility.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000000e0a8000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x000000000e0a8000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000000e0a8000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000000e0a8000 	/usr/lib/libIOReport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000000e0a8000 	/usr/lib/libTLE.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000000e0a8000 	/usr/lib/libmecab.dylib
0x000000000e0a8000 	/usr/lib/libCRFSuite.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000000e0a8000 	/usr/lib/libThaiTokenizer.dylib
0x000000000e0a8000 	/usr/lib/libCheckFix.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x000000000e0a8000 	/usr/lib/libxslt.1.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x000000000e0a8000 	/usr/lib/libcurl.4.dylib
0x000000000e0a8000 	/usr/lib/libcrypto.46.dylib
0x000000000e0a8000 	/usr/lib/libssl.48.dylib
0x000000000e0a8000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000000e0a8000 	/usr/lib/libsasl2.2.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x000000010da06000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib
0x000000000e0a8000 	/usr/lib/libstdc++.6.dylib
0x000000010cfeb000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libverify.dylib
0x000000010d039000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjava.dylib
0x000000010d0e2000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjdwp.dylib
0x000000010d00b000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnpt.dylib
0x000000010d2ad000 	/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x000000010d177000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libinstrument.dylib
0x000000010d0b4000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libzip.dylib
0x000000010d297000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libdt_socket.dylib
0x0000000123b06000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnio.dylib
0x0000000123b42000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnet.dylib
0x0000000123ba0000 	/Users/<USER>/.debugTools/lib/DebugToolsJniLibrary.dylib
0x0000000123ecb000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libmanagement.dylib
0x0000000128ca8000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libsunec.dylib

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:51224,suspend=y,server=n -agentpath:/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/master_2025_09_05_115754.jfr,log=/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/master_2025_09_05_115754.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dsocket.server.port=33833 -javaagent:/Users/<USER>/.debugTools/lib/debug-tools-agent-4.3.1.jar=server=false,printSql=Pretty,traceSql=false,hotswap=true,autoAttach=false,autoSaveSql=false,sqlRetentionDays=1 -Dfile.encoding=UTF-8 
java_command: com.pes.jd.application.UsrmApplication
java_class_path (initial): /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/charsets.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/cldrdata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/dnsns.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/jaccess.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/localedata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/nashorn.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunec.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/zipfs.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jce.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jfr.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jsse.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/management-agent.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/resources.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/rt.jar:/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-master/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.17.RELEASE/spring-boot-starter-web-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.17.RELEASE/spring-boot-starter-tomcat-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repositor
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/Users/<USER>/.nvm/versions/node/v24.5.0/bin:/opt/homebrew/Cellar/node/24.1.0/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/.orbstack/bin
SHELL=/bin/zsh

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x38958c], sa_mask[0]=00000000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 24.6.0 Darwin Kernel Version 24.6.0: Mon Jul 14 11:28:30 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T6030 x86_64
rlimit: STACK 8176k, CORE 0k, NPROC 6000, NOFILE 10240, AS infinity
load average:4.98 4.03 3.54

CPU:total 12 (initial active 12) (1 cores per cpu, 1 threads per core) family 6 model 44 stepping 0, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, aes, clmul, tsc, tscinvbit, tscinv

Memory: 4k page, physical 37748736k(38944k free)

/proc/meminfo:


vm_info: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26) for bsd-amd64 JRE (1.8.0), built on Jul 31 2018 03:28:48 by "jenkins" with gcc 4.8.2

time: Fri Sep  5 13:32:02 2025
elapsed time: 5646 seconds (0d 1h 34m 6s)

