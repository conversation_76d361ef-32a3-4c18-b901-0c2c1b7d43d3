{"permissions": {"allow": ["Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/rt-self-sub/src/main/java/com/pes/jd/business/sub/impl/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/rt-self-sub/src/main/java/com/pes/jd/business/sub/impl/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/rt-self-sub/src/main/java/com/pes/jd/business/sub/impl/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/rt-self-sub/src/main/java/com/pes/jd/business/sub/impl/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob/src/main/java/com/pes/jd/data/api/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob/src/main/java/com/pes/jd/controller/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob/src/main/java/com/pes/jd/business/impl/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob/src/main/java/com/pes/jd/business/impl/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob/src/main/java/com/pes/jd/business/impl/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob/src/main/java/com/pes/jd/controller/**)", "Read(/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob/src/main/java/com/pes/jd/controller/**)", "Read(//Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-web/src/main/java/com/pes/jd/controller/**)"], "deny": [], "ask": []}}