#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x0000000000000000, pid=94739, tid=0x0000000000004213
#
# JRE version: OpenJDK Runtime Environment (8.0) (build 1.8.0_282b08-internal-202101310908-dcevm8u282b08)
# Java VM: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26 mixed mode bsd-amd64 compressed oops)
# Problematic frame:
# C  0x0000000000000000
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://github.com/dcevm/dcevm/issues
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  T H R E A D  ---------------

Current thread (0x00007fde68e0c000):  JavaThread "SIGINT handler" daemon [_thread_in_native, id=16915, stack(0x000000030dcb2000,0x000000030ddb2000)]

siginfo: si_signo: 11 (SIGSEGV), si_code: 1 (SEGV_MAPERR), si_addr: 0x0000000000000000

Registers:
RAX=0x0000000000000000, RBX=0x0000000123b887d8, RCX=0x0000000000000028, RDX=0x0000000000000000
RSP=0x000000030ddb18c8, RBP=0x000000030ddb1910, RSI=0x000000030ddb1920, RDI=0x00007fde68e0c1e0
R8 =0x0000600004fa0870, R9 =0x0000000123b887d8, R10=0x000000010f7313f8, R11=0x000000010dca2ef2
R12=0x0000000000000000, R13=0x0000000123b887d8, R14=0x000000030ddb1928, R15=0x00007fde68e0c000
RIP=0x0000000000000000, EFLAGS=0x0000000000000287, ERR=0x0000000000000004
  TRAPNO=0x000000000000000e

Top of Stack: (sp=0x000000030ddb18c8)
0x000000030ddb18c8:   000000010f731424 000000030ddb18d0
0x000000030ddb18d8:   0000000123b887d8 000000030ddb1928
0x000000030ddb18e8:   0000000123b88f40 0000000000000000
0x000000030ddb18f8:   0000000123b887d8 0000000000000000
0x000000030ddb1908:   000000030ddb1930 000000030ddb1980
0x000000030ddb1918:   000000010f71a07d 000000064080d958
0x000000030ddb1928:   000000010f7281b8 0000000000000001
0x000000030ddb1938:   000000064080d958 000000030ddb1930
0x000000030ddb1948:   0000000123b88cb7 000000030ddb19b0
0x000000030ddb1958:   0000000123b88f40 0000000000000000
0x000000030ddb1968:   00000001206ed410 000000030ddb1930
0x000000030ddb1978:   000000030ddb19b0 000000030ddb19f8
0x000000030ddb1988:   000000010f71a07d 0000000000000000
0x000000030ddb1998:   0000000000000000 000000064080d958
0x000000030ddb19a8:   0000000000000000 0000000000000082
0x000000030ddb19b8:   000000030ddb19b8 00000001206a2cb0
0x000000030ddb19c8:   000000030ddb1a10 00000001206a2dc8
0x000000030ddb19d8:   0000000000000000 00000001206a2cc0
0x000000030ddb19e8:   000000030ddb19b0 000000030ddb1a08
0x000000030ddb19f8:   000000030ddb1a58 000000010f71a0c2
0x000000030ddb1a08:   0000000640965180 00000006408694f8
0x000000030ddb1a18:   000000030ddb1a18 000000012a7df918
0x000000030ddb1a28:   000000030ddb1a68 000000012a7df9c0
0x000000030ddb1a38:   0000000000000000 000000012a7df928
0x000000030ddb1a48:   000000030ddb1a08 000000030ddb1a70
0x000000030ddb1a58:   000000030ddb1b20 0000000110a85514
0x000000030ddb1a68:   0000000582a2f0e0 0000000120544bb8
0x000000030ddb1a78:   00007fde68e0c000 0000000120544bb8
0x000000030ddb1a88:   00007fde68e0c000 0000000582a2f0f8
0x000000030ddb1a98:   000000010da55e36 000000030ddb1b20
0x000000030ddb1aa8:   000000010f7124e7 000000010f7124e7
0x000000030ddb1ab8:   0000000582a2f0f8 0000000300001fa0 

Instructions: (pc=0x0000000000000000)
0xffffffffffffffe0:   

Register to memory mapping:

RAX=0x0000000000000000 is an unknown value
RBX={method} {0x0000000123b887d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
RCX=0x0000000000000028 is an unknown value
RDX=0x0000000000000000 is an unknown value
RSP=0x000000030ddb18c8 is pointing into the stack for thread: 0x00007fde68e0c000
RBP=0x000000030ddb1910 is pointing into the stack for thread: 0x00007fde68e0c000
RSI=0x000000030ddb1920 is pointing into the stack for thread: 0x00007fde68e0c000
RDI=0x00007fde68e0c1e0 is an unknown value
R8 =0x0000600004fa0870 is an unknown value
R9 ={method} {0x0000000123b887d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R10=0x000000010f7313f8 is at code_begin+1528 in an Interpreter codelet
method entry point (kind = native)  [0x000000010f730e00, 0x000000010f731d20]  3872 bytes
R11=0x000000010dca2ef2: throw_unsatisfied_link_error+0 in /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib at 0x000000010d8c9000
R12=0x0000000000000000 is an unknown value
R13={method} {0x0000000123b887d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R14=0x000000030ddb1928 is pointing into the stack for thread: 0x00007fde68e0c000
R15=0x00007fde68e0c000 is a thread


Stack: [0x000000030dcb2000,0x000000030ddb2000],  sp=0x000000030ddb18c8,  free space=1022k
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.Shutdown.beforeHalt()V+0
j  java.lang.Shutdown.exit(I)V+95
j  java.lang.Terminator$1.handle(Lsun/misc/Signal;)V+8
j  sun.misc.Signal$1.run()V+8
J 8527 C1 java.lang.Thread.run()V (17 bytes) @ 0x0000000110a85514 [0x0000000110a854c0+0x54]
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
=>0x00007fde68e0c000 JavaThread "SIGINT handler" daemon [_thread_in_native, id=16915, stack(0x000000030dcb2000,0x000000030ddb2000)]
  0x00007fde6b62b800 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=84831, stack(0x000000030eadc000,0x000000030ebdc000)]
  0x00007fde2c011800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=45579, stack(0x0000000310324000,0x0000000310424000)]
  0x00007fde2a010000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=45371, stack(0x000000030fd12000,0x000000030fe12000)]
  0x00007fde6b935000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=54871, stack(0x000000030fa09000,0x000000030fb09000)]
  0x00007fde2a037000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=51971, stack(0x000000030f803000,0x000000030f903000)]
  0x00007fde2980f800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=38019, stack(0x000000030f5fd000,0x000000030f6fd000)]
  0x00007fde2980d000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=39863, stack(0x000000030f0ee000,0x000000030f1ee000)]
  0x00007fde2980c000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=51479, stack(0x000000030f700000,0x000000030f800000)]
  0x00007fde29012000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=63315, stack(0x000000030f4fa000,0x000000030f5fa000)]
  0x00007fde29808800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=53291, stack(0x0000000311c6f000,0x0000000311d6f000)]
  0x00007fde2c00d800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=65551, stack(0x000000030f906000,0x000000030fa06000)]
  0x00007fde2c80a800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=54583, stack(0x0000000311b6c000,0x0000000311c6c000)]
  0x00007fde68bab000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=85763, stack(0x000000030fb0c000,0x000000030fc0c000)]
  0x00007fde68e01800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=86531, stack(0x0000000311a69000,0x0000000311b69000)]
  0x00007fde6b5ff800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=54379, stack(0x0000000311457000,0x0000000311557000)]
  0x00007fde68dad000 JavaThread "DestroyJavaVM" [_thread_blocked, id=4099, stack(0x000000030d60a000,0x000000030d70a000)]
  0x00007fde6b605000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_in_native, id=50435, stack(0x0000000311760000,0x0000000311860000)]
  0x00007fde2d036000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=57091, stack(0x000000031165d000,0x000000031175d000)]
  0x00007fde68dad800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=57383, stack(0x000000031155a000,0x000000031165a000)]
  0x00007fde2d624800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=49711, stack(0x0000000311354000,0x0000000311454000)]
  0x00007fde68da6000 JavaThread "http-nio-9200-AsyncTimeout" daemon [_thread_blocked, id=49155, stack(0x0000000311251000,0x0000000311351000)]
  0x00007fde6bd28800 JavaThread "http-nio-9200-Acceptor-0" daemon [_thread_in_native, id=58887, stack(0x000000031114e000,0x000000031124e000)]
  0x00007fde68d7d800 JavaThread "http-nio-9200-ClientPoller-1" daemon [_thread_in_native, id=48643, stack(0x000000031104b000,0x000000031114b000)]
  0x00007fde6b5ea000 JavaThread "http-nio-9200-ClientPoller-0" daemon [_thread_in_native, id=59651, stack(0x0000000310f48000,0x0000000311048000)]
  0x00007fde68d7d000 JavaThread "http-nio-9200-exec-10" daemon [_thread_blocked, id=47875, stack(0x0000000310e45000,0x0000000310f45000)]
  0x00007fde68d72800 JavaThread "http-nio-9200-exec-9" daemon [_thread_blocked, id=47619, stack(0x0000000310d42000,0x0000000310e42000)]
  0x00007fde6bd26000 JavaThread "http-nio-9200-exec-8" daemon [_thread_blocked, id=60419, stack(0x0000000310c3f000,0x0000000310d3f000)]
  0x00007fde6a624000 JavaThread "http-nio-9200-exec-7" daemon [_thread_blocked, id=47363, stack(0x0000000310b3c000,0x0000000310c3c000)]
  0x00007fde69271800 JavaThread "http-nio-9200-exec-6" daemon [_thread_blocked, id=60931, stack(0x0000000310a39000,0x0000000310b39000)]
  0x00007fde6946b800 JavaThread "http-nio-9200-exec-5" daemon [_thread_blocked, id=61187, stack(0x0000000310936000,0x0000000310a36000)]
  0x00007fde692b2800 JavaThread "http-nio-9200-exec-4" daemon [_thread_blocked, id=46339, stack(0x0000000310833000,0x0000000310933000)]
  0x00007fde68d92000 JavaThread "http-nio-9200-exec-3" daemon [_thread_blocked, id=61699, stack(0x0000000310730000,0x0000000310830000)]
  0x00007fde6b5e7000 JavaThread "http-nio-9200-exec-2" daemon [_thread_blocked, id=61955, stack(0x000000031062d000,0x000000031072d000)]
  0x00007fde6b574800 JavaThread "http-nio-9200-exec-1" daemon [_thread_blocked, id=62219, stack(0x000000031052a000,0x000000031062a000)]
  0x00007fde6946a800 JavaThread "NioBlockingSelector.BlockPoller-1" daemon [_thread_in_native, id=62983, stack(0x0000000310427000,0x0000000310527000)]
  0x00007fde6ae83000 JavaThread "ThreadPoolTaskScheduler-1" [_thread_blocked, id=63767, stack(0x0000000310221000,0x0000000310321000)]
  0x00007fde6b2e1800 JavaThread "com.alibaba.nacos.naming.push.receiver" daemon [_thread_in_native, id=64519, stack(0x000000031011e000,0x000000031021e000)]
  0x00007fde6b2e0000 JavaThread "com.alibaba.nacos.naming.failover" daemon [_thread_blocked, id=44547, stack(0x000000031001b000,0x000000031011b000)]
  0x00007fde6945a000 JavaThread "com.alibaba.nacos.naming.client.listener" daemon [_thread_blocked, id=44387, stack(0x000000030ff18000,0x0000000310018000)]
  0x00007fde6944b000 JavaThread "spring.cloud.inetutils" daemon [_thread_blocked, id=39203, stack(0x000000030fe15000,0x000000030ff15000)]
  0x00007fde6adda800 JavaThread "commons-pool-evictor-thread" [_thread_blocked, id=38407, stack(0x000000030fc0f000,0x000000030fd0f000)]
  0x00007fde6bd9c000 JavaThread "Druid-ConnectionPool-Destroy-760932238" daemon [_thread_blocked, id=35843, stack(0x000000030efeb000,0x000000030f0eb000)]
  0x00007fde6b202000 JavaThread "Druid-ConnectionPool-Create-760932238" daemon [_thread_blocked, id=13331, stack(0x000000030da9f000,0x000000030db9f000)]
  0x00007fde2aad3800 JavaThread "Abandoned connection cleanup thread" daemon [_thread_blocked, id=34943, stack(0x000000030f3f7000,0x000000030f4f7000)]
  0x00007fde2a8e9800 JavaThread "container-0" [_thread_blocked, id=42559, stack(0x000000030f2f4000,0x000000030f3f4000)]
  0x00007fde6a118800 JavaThread "ContainerBackgroundProcessor[StandardEngine[Tomcat]]" daemon [_thread_blocked, id=43059, stack(0x000000030f1f1000,0x000000030f2f1000)]
  0x00007fde6af5c800 JavaThread "Thread-12" daemon [_thread_blocked, id=25115, stack(0x000000030ece2000,0x000000030ede2000)]
  0x00007fde6b0c5800 JavaThread "com.alibaba.nacos.client.Worker.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=33807, stack(0x000000030eee8000,0x000000030efe8000)]
  0x00007fde6b2f5800 JavaThread "Timer-0" daemon [_thread_blocked, id=33487, stack(0x000000030ede5000,0x000000030eee5000)]
  0x00007fde6892e800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=24839, stack(0x000000030ebdf000,0x000000030ecdf000)]
  0x00007fde6b3ef000 JavaThread "Attach Listener" daemon [_thread_blocked, id=28419, stack(0x000000030e9d9000,0x000000030ead9000)]
  0x00007fde6bb20000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=29759, stack(0x000000030e7d3000,0x000000030e8d3000)]
  0x00007fde6b2bc800 JavaThread "Service Thread" daemon [_thread_blocked, id=30467, stack(0x000000030e6d0000,0x000000030e7d0000)]
  0x00007fde6b29f000 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=22275, stack(0x000000030e5cd000,0x000000030e6cd000)]
  0x00007fde6b29e000 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=31235, stack(0x000000030e4ca000,0x000000030e5ca000)]
  0x00007fde6b286000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=31747, stack(0x000000030e3c7000,0x000000030e4c7000)]
  0x00007fde6b285000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=22031, stack(0x000000030e2c4000,0x000000030e3c4000)]
  0x00007fde6bb00000 JavaThread "Thread-2" daemon [_thread_blocked, id=21771, stack(0x000000030e1c1000,0x000000030e2c1000)]
  0x00007fde6bafd000 JavaThread "HotSwap Dispatcher" daemon [_thread_blocked, id=18179, stack(0x000000030e0be000,0x000000030e1be000)]
  0x00007fde6bad7000 JavaThread "HotSwap Watcher" daemon [_thread_blocked, id=19351, stack(0x000000030dfbb000,0x000000030e0bb000)]
  0x00007fde6b00b800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=17155, stack(0x000000030ddb5000,0x000000030deb5000)]
  0x00007fde6a9dd000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=20739, stack(0x000000030dbaf000,0x000000030dcaf000)]
  0x00007fde6b924800 JavaThread "Finalizer" daemon [_thread_blocked, id=15659, stack(0x000000030d913000,0x000000030da13000)]
  0x00007fde6b00a800 JavaThread "Reference Handler" daemon [_thread_blocked, id=12067, stack(0x000000030d810000,0x000000030d910000)]

Other Threads:
  0x00007fde6b922000 VMThread [stack: 0x000000030d70d000,0x000000030d80d000] [id=11591]
  0x00007fde6bb6d800 WatcherThread [stack: 0x000000030e8d6000,0x000000030e9d6000] [id=29011]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap:
 def new generation   total 177024K, used 44861K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,  28% used [0x0000000580000000, 0x0000000582bb6350, 0x00000005899b0000)
  from space 19648K,   0% used [0x00000005899b0000, 0x00000005899c90f0, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82888K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K

Card table byte_map: [0x000000011e712000,0x000000011f913000] byte_map_base: 0x000000011bb12000

Polling page: 0x000000010cea4000

CodeCache: size=245760Kb used=21651Kb max_used=21651Kb free=224108Kb
 bounds [0x000000010f712000, 0x0000000110c42000, 0x000000011e712000]
 total_blobs=9482 nmethods=8872 adapters=531
 compilation: enabled

Compilation events (10 events):
Event: 60816.129 Thread 0x00007fde6b29f000 9089 % !   1       org.apache.coyote.AbstractProtocol$AsyncTimeout::run @ 0 (102 bytes)
Event: 60816.131 Thread 0x00007fde6b29f000 nmethod 9089% 0x0000000110c33bd0 code [0x0000000110c33da0, 0x0000000110c34138]
Event: 61553.383 Thread 0x00007fde6b29f000 9090   !   1       org.apache.tomcat.util.net.NioBlockingSelector$BlockPoller::run (404 bytes)
Event: 61553.390 Thread 0x00007fde6b29f000 nmethod 9090 0x0000000110c34690 code [0x0000000110c34a60, 0x0000000110c35d88]
Event: 61847.408 Thread 0x00007fde6b29f000 9091   !   1       org.apache.coyote.AbstractProtocol$AsyncTimeout::run (102 bytes)
Event: 61847.412 Thread 0x00007fde6b29f000 nmethod 9091 0x0000000110c38110 code [0x0000000110c382e0, 0x0000000110c38638]
Event: 75325.455 Thread 0x00007fde6b29f000 9092       1       java.util.concurrent.TimeUnit$5::toNanos (11 bytes)
Event: 75325.465 Thread 0x00007fde6b29f000 nmethod 9092 0x0000000110c38b50 code [0x0000000110c38ca0, 0x0000000110c38e10]
Event: 75925.465 Thread 0x00007fde6b29f000 9093   !   1       java.util.concurrent.LinkedBlockingQueue::poll (134 bytes)
Event: 75925.478 Thread 0x00007fde6b29f000 nmethod 9093 0x0000000110c38ed0 code [0x0000000110c39160, 0x0000000110c39b78]

GC Heap History (10 events):
Event: 75974.437 GC heap before
{Heap before GC invocations=239 (full 3):
 def new generation   total 177024K, used 157476K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,   0% used [0x000000058ace0000, 0x000000058acf90f8, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
Event: 75974.463 GC heap after
Heap after GC invocations=240 (full 3):
 def new generation   total 177024K, used 100K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,   0% used [0x00000005899b0000, 0x00000005899c9178, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
}
Event: 76336.251 GC heap before
{Heap before GC invocations=240 (full 3):
 def new generation   total 177024K, used 157476K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,   0% used [0x00000005899b0000, 0x00000005899c9178, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
Event: 76336.276 GC heap after
Heap after GC invocations=241 (full 3):
 def new generation   total 177024K, used 100K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,   0% used [0x000000058ace0000, 0x000000058acf9138, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
}
Event: 76699.458 GC heap before
{Heap before GC invocations=241 (full 3):
 def new generation   total 177024K, used 157476K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,   0% used [0x000000058ace0000, 0x000000058acf9138, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
Event: 76699.485 GC heap after
Heap after GC invocations=242 (full 3):
 def new generation   total 177024K, used 100K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,   0% used [0x00000005899b0000, 0x00000005899c93f8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
}
Event: 77063.658 GC heap before
{Heap before GC invocations=242 (full 3):
 def new generation   total 177024K, used 157476K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,   0% used [0x00000005899b0000, 0x00000005899c93f8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
Event: 77063.675 GC heap after
Heap after GC invocations=243 (full 3):
 def new generation   total 177024K, used 101K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,   0% used [0x000000058ace0000, 0x000000058acf9588, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
}
Event: 77426.256 GC heap before
{Heap before GC invocations=243 (full 3):
 def new generation   total 177024K, used 157477K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,   0% used [0x000000058ace0000, 0x000000058acf9588, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
Event: 77426.267 GC heap after
Heap after GC invocations=244 (full 3):
 def new generation   total 177024K, used 100K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,   0% used [0x00000005899b0000, 0x00000005899c90f0, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 88181K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  22% used [0x0000000640000000, 0x000000064561d658, 0x000000064561d800, 0x0000000658000000)
 Metaspace       used 82886K, capacity 84106K, committed 84352K, reserved 1124352K
  class space    used 9436K, capacity 9743K, committed 9856K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Internal exceptions (10 events):
Event: 77474.372 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x00000005815cc0b8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77474.372 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x00000005815cc738) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77484.375 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x00000005819030c8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77484.376 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x0000000581903748) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77494.387 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x0000000581f14ff0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77494.387 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x0000000581f15670) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77504.389 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x0000000582241ac8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77504.389 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x0000000582242148) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77514.392 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x0000000582581b68) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 77514.392 Thread 0x00007fde6b2f5800 Exception <a 'java/io/FileNotFoundException'> (0x00000005825821e8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]

Events (10 events):
Event: 77523.261 Executing VM operation: ChangeBreakpoints done
Event: 77523.262 Thread 0x00007fde6a80e800 Thread exited: 0x00007fde6a80e800
Event: 77523.263 Executing VM operation: ChangeBreakpoints
Event: 77523.263 Executing VM operation: ChangeBreakpoints done
Event: 77523.263 Executing VM operation: ChangeBreakpoints
Event: 77523.263 Executing VM operation: ChangeBreakpoints done
Event: 77523.267 Thread 0x00007fde6a80d800 Thread exited: 0x00007fde6a80d800
Event: 77523.278 loading class sun/misc/Signal$1
Event: 77523.284 loading class sun/misc/Signal$1 done
Event: 77523.285 Thread 0x00007fde68e0c000 Thread added: 0x00007fde68e0c000


Dynamic libraries:
0x000000000e0a8000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x000000000e0a8000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000000e0a8000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000000e0a8000 	/usr/lib/libSystem.B.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x000000000e0a8000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000000e0a8000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000000e0a8000 	/usr/lib/libspindump.dylib
0x000000000e0a8000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000000e0a8000 	/usr/lib/libbsm.0.dylib
0x000000000e0a8000 	/usr/lib/libapp_launch_measurement.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000000e0a8000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x000000000e0a8000 	/usr/lib/liblangid.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000000e0a8000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000000e0a8000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000000e0a8000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000000e0a8000 	/usr/lib/libz.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000000e0a8000 	/usr/lib/libicucore.A.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000000e0a8000 	/usr/lib/libMobileGestalt.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000000e0a8000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x000000000e0a8000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000000e0a8000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000000e0a8000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000000e0a8000 	/usr/lib/libenergytrace.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x000000000e0a8000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000000e0a8000 	/usr/lib/libxml2.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000000e0a8000 	/usr/lib/libobjc.A.dylib
0x000000000e0a8000 	/usr/lib/libc++.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000000e0a8000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000000e0a8000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000000e0a8000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000000e0a8000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x000000000e0a8000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x000000000e0a8000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x000000000e0a8000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x000000000e0a8000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x000000000e0a8000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreImage.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDataDetection.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDispatch.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftIOKit.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftMetal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftOSLog.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObservation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftXPC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_errno.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_math.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_signal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_stdio.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftos.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsimd.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsys_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftunistd.dylib
0x000000000e0a8000 	/usr/lib/libcompression.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000000e0a8000 	/usr/lib/libate.dylib
0x000000000e0a8000 	/usr/lib/system/libcache.dylib
0x000000000e0a8000 	/usr/lib/system/libcommonCrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libcompiler_rt.dylib
0x000000000e0a8000 	/usr/lib/system/libcopyfile.dylib
0x000000000e0a8000 	/usr/lib/system/libcorecrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libdispatch.dylib
0x000000000e0a8000 	/usr/lib/system/libdyld.dylib
0x000000000e0a8000 	/usr/lib/system/libkeymgr.dylib
0x000000000e0a8000 	/usr/lib/system/libmacho.dylib
0x000000000e0a8000 	/usr/lib/system/libquarantine.dylib
0x000000000e0a8000 	/usr/lib/system/libremovefile.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_asl.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_blocks.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_c.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_collections.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_configuration.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwin.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_info.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_m.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_malloc.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_notify.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sandbox.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_secinit.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_kernel.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_platform.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_pthread.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_trace.dylib
0x000000000e0a8000 	/usr/lib/system/libunwind.dylib
0x000000000e0a8000 	/usr/lib/system/libxpc.dylib
0x000000000e0a8000 	/usr/lib/libc++abi.dylib
0x000000000e0a8000 	/usr/lib/libRosetta.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000000e0a8000 	/usr/lib/liboah.dylib
0x000000000e0a8000 	/usr/lib/libfakelink.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x000000000e0a8000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x000000000e0a8000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000000e0a8000 	/usr/lib/libapple_nghttp2.dylib
0x000000000e0a8000 	/usr/lib/libsqlite3.dylib
0x000000000e0a8000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000000e0a8000 	/usr/lib/system/libkxld.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x000000000e0a8000 	/usr/lib/libCoreEntitlements.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000000e0a8000 	/usr/lib/libcoretls.dylib
0x000000000e0a8000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000000e0a8000 	/usr/lib/libpam.2.dylib
0x000000000e0a8000 	/usr/lib/libxar.1.dylib
0x000000000e0a8000 	/usr/lib/libarchive.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftSystem.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x000000000e0a8000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x000000000e0a8000 	/usr/lib/libboringssl.dylib
0x000000000e0a8000 	/usr/lib/libdns_services.dylib
0x000000000e0a8000 	/usr/lib/libquic.dylib
0x000000000e0a8000 	/usr/lib/libusrtcp.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x000000000e0a8000 	/usr/lib/swift/libswiftDistributed.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSynchronization.dylib
0x000000000e0a8000 	/usr/lib/libnetwork.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000000e0a8000 	/usr/lib/libAppleArchive.dylib
0x000000000e0a8000 	/usr/lib/libbz2.1.0.dylib
0x000000000e0a8000 	/usr/lib/liblzma.5.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000000e0a8000 	/usr/lib/libgermantok.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000000e0a8000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000000e0a8000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000000e0a8000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x000000000e0a8000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000000e0a8000 	/usr/lib/libutil.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x000000000e0a8000 	/usr/lib/libhvf.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_RegexParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000000e0a8000 	/usr/lib/libexpat.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x000000000e0a8000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000000e0a8000 	/usr/lib/libcups.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000000e0a8000 	/usr/lib/libresolv.9.dylib
0x000000000e0a8000 	/usr/lib/libiconv.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000000e0a8000 	/usr/lib/libheimdal-asn1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000000e0a8000 	/usr/lib/libcharset.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000000e0a8000 	/usr/lib/libAudioStatistics.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x000000000e0a8000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000000e0a8000 	/usr/lib/libSMC.dylib
0x000000000e0a8000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000000e0a8000 	/usr/lib/libperfcheck.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x000000000e0a8000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x000000000e0a8000 	/usr/lib/libmis.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x000000000e0a8000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000000e0a8000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000000e0a8000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000000e0a8000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x000000000e0a8000 	/usr/lib/libAccessibility.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000000e0a8000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x000000000e0a8000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000000e0a8000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000000e0a8000 	/usr/lib/libIOReport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000000e0a8000 	/usr/lib/libTLE.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000000e0a8000 	/usr/lib/libmecab.dylib
0x000000000e0a8000 	/usr/lib/libCRFSuite.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000000e0a8000 	/usr/lib/libThaiTokenizer.dylib
0x000000000e0a8000 	/usr/lib/libCheckFix.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x000000000e0a8000 	/usr/lib/libxslt.1.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x000000000e0a8000 	/usr/lib/libcurl.4.dylib
0x000000000e0a8000 	/usr/lib/libcrypto.46.dylib
0x000000000e0a8000 	/usr/lib/libssl.48.dylib
0x000000000e0a8000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000000e0a8000 	/usr/lib/libsasl2.2.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x000000010d8c9000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib
0x000000000e0a8000 	/usr/lib/libstdc++.6.dylib
0x000000010ceae000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libverify.dylib
0x000000010cefc000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjava.dylib
0x000000010cfa5000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjdwp.dylib
0x000000010cece000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnpt.dylib
0x000000010d170000 	/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x000000010d03a000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libinstrument.dylib
0x000000010cf77000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libzip.dylib
0x000000010d8a2000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libdt_socket.dylib
0x00000001239d3000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnio.dylib
0x0000000123a0f000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnet.dylib
0x0000000123a6c000 	/Users/<USER>/.debugTools/lib/DebugToolsJniLibrary.dylib
0x0000000123dac000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libmanagement.dylib
0x00000001287cf000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libsunec.dylib

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:55393,suspend=y,server=n -agentpath:/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/master_2025_09_04_135116.jfr,log=/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/master_2025_09_04_135116.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dsocket.server.port=33833 -javaagent:/Users/<USER>/.debugTools/lib/debug-tools-agent-4.3.1.jar=server=false,printSql=Pretty,traceSql=false,hotswap=true,autoAttach=false,autoSaveSql=false,sqlRetentionDays=1 -Dfile.encoding=UTF-8 
java_command: com.pes.jd.application.UsrmApplication
java_class_path (initial): /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/charsets.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/cldrdata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/dnsns.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/jaccess.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/localedata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/nashorn.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunec.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/zipfs.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jce.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jfr.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jsse.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/management-agent.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/resources.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/rt.jar:/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-master/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.17.RELEASE/spring-boot-starter-web-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.17.RELEASE/spring-boot-starter-tomcat-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repositor
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/Users/<USER>/.nvm/versions/node/v24.5.0/bin:/opt/homebrew/Cellar/node/24.1.0/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/.orbstack/bin
SHELL=/bin/zsh

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x38958c], sa_mask[0]=00000000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 24.6.0 Darwin Kernel Version 24.6.0: Mon Jul 14 11:28:30 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T6030 x86_64
rlimit: STACK 8176k, CORE 0k, NPROC 6000, NOFILE 10240, AS infinity
load average:2.58 2.83 2.80

CPU:total 12 (initial active 12) (1 cores per cpu, 1 threads per core) family 6 model 44 stepping 0, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, aes, clmul, tsc, tscinvbit, tscinv

Memory: 4k page, physical 37748736k(31972k free)

/proc/meminfo:


vm_info: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26) for bsd-amd64 JRE (1.8.0), built on Jul 31 2018 03:28:48 by "jenkins" with gcc 4.8.2

time: Fri Sep  5 11:23:21 2025
elapsed time: 77523 seconds (0d 21h 32m 3s)

