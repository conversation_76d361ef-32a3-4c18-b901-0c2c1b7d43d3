{"folders": [{"name": "frontend", "path": "../../../../../../../frontend"}, {"name": "y<PERSON><PERSON><PERSON>-master", "path": "../../.."}, {"name": "yiyitech-pop-web", "path": "../../../../yiyitech-pop-web"}, {"name": "yiyitech-pop-dailysub", "path": "../../../../yiyitech-pop-dailysub"}, {"name": "yiyitech-pop-dailyjob", "path": "../../../../yiyitech-pop-dailyjob"}, {"name": "yiyitech-self-web", "path": "../../../../../yiyitech-self-web"}, {"name": "yiyitech-self-dailysub", "path": "../../../../../yiyitech-self-dailysub"}, {"name": "yiyitech-self-dailyjob", "path": "../../../../../../maybeErrorProject/yiyitech-self-dailyjob"}, {"name": "jdonginterface", "path": "../../../../../cursor/jdonginterface"}, {"name": "rt-self-sub", "path": "../../../../rt-self-sub"}, {"name": "rt-self-job", "path": "../../../../rt-self-job"}, {"name": "yiyitech-pop-rtjob", "path": "../../../../yiyitech-pop-rtjob"}, {"name": "yiyitech-pop-rtsub", "path": "../../../../yiyitech-pop-rtsub"}, {"name": "yiyitech-order-push", "path": "../../../../yiyitech-order-push"}, {"name": "connentEs", "path": "../../../../../../../mypthonProject/connentEs"}], "settings": {"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "interactive"}}