[{"field": "shopSaleAmount", "detail": "店铺销售额", "tableName": "pes_shop_ov_day", "type": "double"}, {"field": "csSaleRate", "detail": "客服销售占比", "tableName": "pes_cs_performance", "type": "double"}, {"field": "shopSilentSaleAmount", "detail": "静默销售额", "tableName": "pes_shop_ov_day", "type": "double"}, {"field": "shopPaidTradeNum", "detail": "店铺成交单数", "tableName": "pes_shop_ov_day", "type": "integer"}, {"field": "shopSaleBuyerNum", "detail": "店铺付款人数", "tableName": "pes_shop_ov_day", "type": "integer"}, {"field": "shopConsignNum", "detail": "店铺发货单数", "tableName": "pes_shop_ov_day", "type": "integer"}, {"field": "shopPerCusPrice", "detail": "店铺客单价", "tableName": "pes_shop_ov_day", "type": "double"}, {"field": "shopCreatedTradeAmount", "detail": "店铺下单金额", "tableName": "pes_shop_ov_day", "type": "double"}, {"field": "shopConfirmGoodsAmount", "detail": "店铺确认收货金额", "tableName": "pes_shop_ov_day", "type": "double"}, {"field": "dsrItemRating", "detail": "商品评分", "tableName": "pes_shop_dsr", "type": "double"}, {"field": "dsrServiceRating", "detail": "服务态度", "tableName": "pes_shop_dsr", "type": "double"}, {"field": "dsrDeliveryRating", "detail": "物流速度", "tableName": "pes_shop_dsr", "type": "double"}]