<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsWordnumMapper" >
  <resultMap id="CsWordnumDO" type="com.pes.jd.model.DO.CsWordnum" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="cs_word_num" property="csWordNum" jdbcType="INTEGER" />
    <result column="cs_chat_num" property="csChatNum" jdbcType="INTEGER" />
    <result column="buyer_chat_num" property="buyerChatNum" jdbcType="INTEGER" />
    <result column="qa_rate" property="qaRate" jdbcType="DOUBLE" />
  </resultMap>
  
  <sql id="base_field" >
    id, shop_id, date, cs_nick, cs_word_num, cs_chat_num, buyer_chat_num, qa_rate
  </sql>
 
  <insert id="insertCsWordnum" parameterType="com.pes.jd.model.DO.CsWordnum" >
    INSERT INTO pes_cs_wordnum (shop_id, date, cs_nick, cs_word_num, cs_chat_num,  buyer_chat_num, qa_rate)
    VALUES 
    (	  
    	  #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
	      #{csNick,jdbcType=VARCHAR}, #{csWordNum,jdbcType=INTEGER}, #{csChatNum,jdbcType=INTEGER}, 
	      #{buyerChatNum,jdbcType=INTEGER}, #{qaRate,jdbcType=DOUBLE}
	      
	  )
  </insert>
  
  <delete id="deleteCsWordnumById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_wordnum
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateCsWordnumBySelective" parameterType="com.pes.jd.model.DO.CsWordnum" >
    UPDATE pes_cs_wordnum
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="csWordNum != null" >
        cs_word_num = #{csWordNum,jdbcType=INTEGER},
      </if>
      <if test="csChatNum != null" >
        cs_chat_num = #{csChatNum,jdbcType=INTEGER},
      </if>
      <if test="buyerChatNum != null" >
        buyer_chat_num = #{buyerChatNum,jdbcType=INTEGER},
      </if>
      <if test="qaRate != null" >
        qa_rate = #{qaRate,jdbcType=DOUBLE},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="getCsWordnumById" resultMap="CsWordnumDO" parameterType="java.lang.Long" >
    SELECT 
   		 <include refid="base_field" />
    FROM pes_cs_wordnum
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectCsWordnumByNick" resultMap="CsWordnumDO" parameterType="java.util.Map" >
    SELECT
    <foreach collection="columns" item="item" separator=",">
      ${item}
    </foreach>
    , cs_nick
    FROM pes_cs_wordnum
    WHERE
    cs_nick in
    <foreach collection="nicks" item="nick" open="(" close=")" separator=",">
      #{nick}
    </foreach>
  </select>

  <!-- for client performance -->
  <select id="selectCsWordnumShopIdAndNick" resultType="java.util.Map" parameterType="java.util.Map" >
    SELECT cs_nick nick,date,shop_id shopId,
    sum(cs_word_num) wordsNum,
    sum(cs_chat_num) csChatNum,
    sum(buyer_chat_num) buyerChatNum,
    avg(qaRate) qaRate
    FROM (
      SELECT cs_nick,date,shop_id,
      sum(cs_word_num) cs_word_num,
      sum(cs_chat_num) cs_chat_num,
      sum(buyer_chat_num) buyer_chat_num,
      avg(qa_rate) qaRate
      FROM ${tableName}
      WHERE
      shop_id IN
      <foreach collection="nicks" item="item" separator="," open="(" close=")">
        #{item.shopId}
      </foreach>
      AND cs_nick IN
      <foreach collection="nicks" item="item" separator="," open="(" close=")">
        #{item.nick}
      </foreach>
      AND date BETWEEN #{startDate} AND #{endDate}
--       AND qa_rate !=0
      GROUP BY ${groupBy}
    ) tab GROUP BY ${groupBy}
  </select>
</mapper>