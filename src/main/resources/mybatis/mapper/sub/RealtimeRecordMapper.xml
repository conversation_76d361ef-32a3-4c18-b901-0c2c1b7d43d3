<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.RealtimeRecordMapper">
  <resultMap id="RealtimeRecordDO" type="com.pes.jd.model.DO.RealtimeRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="pull_date" jdbcType="TIMESTAMP" property="pullDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="base_field">
    id, shop_id, nick, pull_date, status
  </sql>
  
   <insert id="insertRealtimeRecord" parameterType="com.pes.jd.model.DO.RealtimeRecord">
    INSERT INTO pes_realtime_record (shop_id, nick,  pull_date, status)
    VALUES 
    (
    	 #{shopId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR}, 
     	 #{pullDate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}
    )
  </insert>
  
  <delete id="deleteRealtimeRecordById" parameterType="java.lang.Long">
    DELETE FROM pes_realtime_record
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
 
  <update id="updateRealtimeRecordBySelective" parameterType="com.pes.jd.model.DO.RealtimeRecord">
    UPDATE pes_realtime_record
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="nick != null">
        nick = #{nick,jdbcType=VARCHAR},
      </if>
      <if test="pullDate != null">
        pull_date = #{pullDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="getRealtimeRecordById" parameterType="java.lang.Long" resultMap="RealtimeRecordDO">
    SELECT 
    	<include refid="base_field" />
    FROM pes_realtime_record
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>
</mapper>