<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsOrderEvaluationMapper" >
  <resultMap id="CsTradeEvaluationDO" type="com.pes.jd.model.DO.CsOrderEvaluation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="good_rating_num" property="goodRatingNum" jdbcType="INTEGER" />
    <result column="bad_rating_num" property="badRatingNum" jdbcType="INTEGER" />
    <result column="neutral_rating_num" property="neutralRatingNum" jdbcType="INTEGER" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, cs_nick, good_rating_num, bad_rating_num, neutral_rating_num
  </sql>
  <select id="getCsTradeEvaluationById" resultMap="CsTradeEvaluationDO" parameterType="java.lang.Long" >
    SELECT 
    <include refid="base_field" />
    FROM pes_cs_order_evaluation
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectCsOrderedEvaluationByNick" resultMap="CsTradeEvaluationDO" parameterType="java.util.Map" >
    SELECT
    <foreach collection="columns" item="item" separator=",">
      ${item}
    </foreach>
    , cs_nick
    FROM pes_cs_order_evaluation
    WHERE
    cs_nick in
    <foreach collection="nicks" item="nick" open="(" close=")" separator=",">
      #{nick}
    </foreach>
  </select>

  <delete id="deleteCsTradeEvaluationById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_order_evaluation
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertCsTradeEvaluation" parameterType="com.pes.jd.model.DO.CsOrderEvaluation" >
    INSERT INTO pes_cs_order_evaluation (id, shop_id, date, 
      cs_nick, good_rating_num, bad_rating_num, 
      neutral_rating_num)
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{goodRatingNum,jdbcType=INTEGER}, #{badRatingNum,jdbcType=INTEGER}, 
      #{neutralRatingNum,jdbcType=INTEGER})
  </insert>
  <update id="updateCsTradeEvaluationById" parameterType="com.pes.jd.model.DO.CsOrderEvaluation" >
    UPDATE pes_cs_order_evaluation
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="goodRatingNum != null" >
        good_rating_num = #{goodRatingNum,jdbcType=INTEGER},
      </if>
      <if test="badRatingNum != null" >
        bad_rating_num = #{badRatingNum,jdbcType=INTEGER},
      </if>
      <if test="neutralRatingNum != null" >
        neutral_rating_num = #{neutralRatingNum,jdbcType=INTEGER},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>