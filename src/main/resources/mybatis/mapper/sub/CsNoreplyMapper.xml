<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsNoreplyMapper" >
  <resultMap id="CsNoreplyDO" type="com.pes.jd.model.DO.CsNoreply" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="no_reply_num" property="noReplyNum" jdbcType="INTEGER" />
    <result column="no_reply_cs_nick" property="noReplyCsNick" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, cs_nick, no_reply_num, no_reply_cs_nick
  </sql>
  <select id="getCsNoreplyById" resultMap="CsNoreplyDO" parameterType="java.lang.Long" >
  SELECT
  <include refid="base_field" />
  FROM pes_cs_noreply
  WHERE id = #{id,jdbcType=BIGINT}
</select>


  <!-- for client performance -->
  <select id="selectCsNoreplyByNickShop" resultType="java.util.Map" parameterType="java.util.Map" >
    SELECT cs_nick nick,date,SUM(no_reply_num)  noReplyNum,shop_id shopId FROM (
      SELECT cs_nick,date,SUM(no_reply_num) no_reply_num,shop_id
      FROM ${tableName}
      WHERE
      shop_id IN
      <foreach collection="nicks" item="item" separator="," open="(" close=")">
        #{item.shopId}
      </foreach>
      AND cs_nick IN
      <foreach collection="nicks" item="item" separator="," open="(" close=")">
        #{item.nick}
      </foreach>
      AND date BETWEEN #{startDate} AND #{endDate}
      GROUP BY ${groupBy}
    ) tab GROUP BY ${groupBy}
  </select>

  <select id="selectCsNoreplyByNick" resultMap="CsNoreplyDO" parameterType="java.util.Map" >
  SELECT
  <foreach collection="columns" item="item" separator=",">
    ${item}
  </foreach>
  , cs_nick
  FROM pes_cs_noreply
  WHERE
  cs_nick in
  <foreach collection="nicks" item="nick" open="(" close=")" separator=",">
    #{nick}
  </foreach>
</select>

  <select id="selectByNickShopDate" resultType="map" parameterType="java.util.Map" >
    SELECT
    <include refid="base_field"/>
    FROM ${tableName}
    cs_nick = #{nick} AND shop_id = #{shopId} and date BETWEEN #{startDate} AND #{endDate}
  </select>

  <delete id="deleteCsNoreplyById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_noreply
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertCsNoreply" parameterType="com.pes.jd.model.DO.CsNoreply" >
    INSERT INTO pes_cs_noreply (id, shop_id, date, 
      cs_nick, no_reply_num, no_reply_cs_nick
      )
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{noReplyNum,jdbcType=INTEGER}, #{noReplyCsNick,jdbcType=LONGVARCHAR}
      )
  </insert>
  <update id="updateCsNoreplyById" parameterType="com.pes.jd.model.DO.CsNoreply" >
    UPDATE pes_cs_noreply
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="noReplyNum != null" >
        no_reply_num = #{noReplyNum,jdbcType=INTEGER},
      </if>
      <if test="noReplyCsNick != null" >
        no_reply_cs_nick = #{noReplyCsNick,jdbcType=LONGVARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>