<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.FeatureConversionLogMapper">

    <resultMap id="FeatureConversionLogDO" type="com.pes.jd.model.DO.FeatureConversionLog">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="feature_location_id" jdbcType="INTEGER" property="featureLocationId" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
        <result column="opt_type" jdbcType="TINYINT" property="optType" />
        <result column="user_level" jdbcType="TINYINT" property="userLevel" />
        <result column="created" jdbcType="TIMESTAMP" property="created" />
    </resultMap>

    <insert id="insert" parameterType="com.pes.jd.model.DO.FeatureConversionLog" 
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO pes_feature_conversion_log (
            feature_location_id,
            shop_id,
            cs_nick,
            opt_type,
            user_level,
            created
        ) VALUES (
            #{featureLocationId,jdbcType=INTEGER},
            #{shopId,jdbcType=BIGINT},
            #{csNick,jdbcType=VARCHAR},
            #{optType,jdbcType=TINYINT},
            #{userLevel,jdbcType=TINYINT},
            #{created,jdbcType=TIMESTAMP}
        )
    </insert>

</mapper>