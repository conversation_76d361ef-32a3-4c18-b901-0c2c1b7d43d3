package com.pes.jd.typehandler;

import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/13 2:35 PM
 * @since 1.0.0
 */
@Component
public class SwapTypeHandler {

    private final static Logger LOGGER = LoggerFactory.getLogger(SwapTypeHandler.class);

    private final Configuration configuration;

    private final SqlSessionFactory sqlSessionFactory;

    private final TypeHandlerRegistry typeHandlerRegistry;

    public SwapTypeHandler(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
        configuration = sqlSessionFactory.getConfiguration();
        typeHandlerRegistry = configuration.getTypeHandlerRegistry();
    }

    @PostConstruct
    public void init(){
        typeHandlerRegistry.register(Double.class,new DoubleNonNullTypeHandler());
        typeHandlerRegistry.register(BigDecimal.class,new BigDecimalNonNullTypeHandler());
        typeHandlerRegistry.register(Integer.class,new IntegerNonNullTypeHandler());
        LOGGER.info("=============替换(type-handler)==========");
    }

}
