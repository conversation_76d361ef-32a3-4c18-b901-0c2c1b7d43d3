package com.pes.jd.dao.impl;

import com.pes.jd.dao.CsReplyQualityDao;
import com.pes.jd.mapper.CsrReplyQualityMapper;
import com.pes.jd.model.DO.CsrReplyQualityDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**  
 * ClassName:CsrReplyQualityDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 上午9:24:18 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Repository
public class CsReplyQualityDaoImpl implements CsReplyQualityDao{

	@Autowired
	private CsrReplyQualityMapper csrReplyQualityMapper;
	
	@Override
	public int insertCsrReplyQuality(CsrReplyQualityDO csrReplyQuality) {
		return csrReplyQualityMapper.insertCsrReplyQuality(csrReplyQuality);
	}

	@Override
	public int batchInsertCsrReplyQuality(JobShopDTO shop, List<CsrReplyQualityDO> csReplyQualityLst) {
		String tableName = CommonUtils.getTableName(shop.getRtSchemaId(), "pes_cs_reply_quality");
		return csrReplyQualityMapper.batchInsertCsrReplyQuality(csReplyQualityLst, tableName);
	}
	
	@Override
	public int deleteCsrReplyQualityById(Long id) {
		return csrReplyQualityMapper.deleteCsrReplyQualityById(id);
	}
	
	@Override
	public int deleteCsrReplyQualityByDate(JobShopDTO shop, Date startDate, Date endDate) {
		
		String tableName = CommonUtils.getTableName(shop.getRtSchemaId(), "pes_cs_reply_quality");
		return csrReplyQualityMapper.deleteCsrReplyQualityByDate(shop.getShopId(),startDate,endDate,tableName);
	}
	
	@Override
	public int updateCsrReplyQualityById(CsrReplyQualityDO csrReplyQuality) {
		return csrReplyQualityMapper.updateCsrReplyQualityById(csrReplyQuality);
	}

	@Override
	public CsrReplyQualityDO getCsrReplyQualityById(Long id) {
		return csrReplyQualityMapper.getCsrReplyQualityById(id);
	}



}
  
