package com.pes.jd.dao.impl;

import com.pes.jd.dao.ReceiveSessionNumHourlyDao;
import com.pes.jd.mapper.ReceiveSessionNumHourlyMapper;
import com.pes.jd.model.DO.ReceiveSessionNumHourlyDO;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public class ReceiveSessionNumHourlyDaoImpl implements ReceiveSessionNumHourlyDao {

    @Autowired
    private ReceiveSessionNumHourlyMapper receiveSessionNumHourlyMapper;

    @Override
    public int deleteByTimePoint(Date date, String schemaId, Long shopId, String nick) {
        final String tableName = CommonUtils.getTableNameOfYear(schemaId, date, "pes_receive_session_num_hourly");
        return receiveSessionNumHourlyMapper.deleteByTimePoint(date,tableName,shopId,nick);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return 0;
    }

    @Override
    public int insert(ReceiveSessionNumHourlyDO record,String schema) {
        final String tableName = CommonUtils.getTableNameOfYear(schema, record.getDate(), "pes_receive_session_num_hourly");
        return receiveSessionNumHourlyMapper.insert(record,tableName);
    }

    @Override
    public int insertSelective(ReceiveSessionNumHourlyDO record) {
        return 0;
    }

    @Override
    public ReceiveSessionNumHourlyDO selectByPrimaryKey(Long id) {
        return null;
    }

    @Override
    public int updateByPrimaryKeySelective(ReceiveSessionNumHourlyDO record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(ReceiveSessionNumHourlyDO record) {
        return 0;
    }
}
