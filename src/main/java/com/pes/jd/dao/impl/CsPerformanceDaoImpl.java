package com.pes.jd.dao.impl;

import com.google.common.collect.Lists;
import com.pes.jd.dao.CsPerformanceDao;
import com.pes.jd.mapper.CsPerformanceMapper;
import com.pes.jd.model.DO.CsPerformanceDO;
import com.pes.jd.model.DTO.CsPerformanceDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * ClassName:CsPerformanceDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 上午9:24:18 <br/>
 * <AUTHOR>
 * @version
 * @since    JDK 1.8
 * @see
 */
@Repository
public class CsPerformanceDaoImpl implements CsPerformanceDao{


	@Autowired
	private CsPerformanceMapper csPerformanceMapper;

	@Override
	public CsPerformanceDO getCsPerformanceById(Long id) {
		return csPerformanceMapper.getCsPerformanceById(id);
	}

	@Override
	public int insertCsPerformance(JobShopDTO shop, Date date, CsPerformanceDO csDayPerformance) {
		if(csDayPerformance == null){
			return 0;
		}
		List<CsPerformanceDO> lst = Lists.newArrayListWithCapacity(1);
		lst.add(csDayPerformance);
		return this.insertTargetDateCsPerformances(shop, date, lst);
	}
	
	@Override
	public int insertTargetDateCsPerformances(JobShopDTO shop, Date date, List<CsPerformanceDO> csDayPerformanceLst) {
		if(CollectionUtils.isEmpty(csDayPerformanceLst)){
			return 0;
		}

		String tableName = CommonUtils.getTableName(shop.getRtSchemaId(), "pes_cs_performance");
		return csPerformanceMapper.batchInsertCsPerformance(shop.getShopId(), date, csDayPerformanceLst, tableName);
	}


	@Override
	public int deleteCsPerformanceById(Long id) {
		return csPerformanceMapper.deleteCsPerformanceById(id);
	}

	@Override
	public int deleteShopCsPerformanceByDate(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getRtSchemaId(), "pes_cs_performance");
		return csPerformanceMapper.deleteShopCsPerformanceByDate(shop.getShopId(), date, tableName);
	}

	@Override
	public int deleteShopCsPerformanceByDateRange(JobShopDTO shop, Date startDate, Date endDate) {
		
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfYear(startDate, endDate, shop.getRtSchemaId(), "pes_cs_performance");
		
		int rows = 0;
		for (DateRangeParam drp : tableNames) {
			
			rows += csPerformanceMapper.deleteShopCsPerformanceByDateRange(shop.getShopId(), drp.getBeginDate(), drp.getEndDate(), drp.getTableName());
		}
		return rows;
	}


	@Override
	public int updateCsPerformanceById(CsPerformanceDO csPerformance) {
		return csPerformanceMapper.updateCsPerformanceById(csPerformance);
	}
	
	@Override
	public List<CsPerformanceDTO> searchByDateShopCs(List<String> nicks, Long shopId, Date startDate, Date endDate, String groupBy,String tableName) {
		return csPerformanceMapper.searchByDateShopCs(nicks, shopId, startDate, endDate, groupBy,tableName);
	}





}

