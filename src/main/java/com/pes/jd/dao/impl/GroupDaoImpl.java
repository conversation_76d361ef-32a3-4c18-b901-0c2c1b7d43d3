package com.pes.jd.dao.impl;

import com.pes.jd.dao.GroupDao;
import com.pes.jd.mapper.GroupMapper;
import com.pes.jd.model.DO.Group;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * ClassName:GroupDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:28:42 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository
public class GroupDaoImpl implements GroupDao {

	@Autowired
	private GroupMapper groupMapper;

	@Override
	public int deleteGroupByGroupId(Long groupId) {
		return groupMapper.deleteGroupByGroupId(groupId);
	}

	@Override
	public int insertGroup(Group record) {
		return groupMapper.insertGroup(record);
	}

	@Override
	public Group getGroupByGroupId(Long groupId) {
		return groupMapper.getGroupByGroupId(groupId);
	}

	@Override
	public int updateGroupBySelective(Group record) {
		return groupMapper.updateGroupBySelective(record);
	}

}
