package com.pes.jd.dao.impl;

import com.pes.jd.dao.LostRecordNoteDao;
import com.pes.jd.mapper.LostRecordNoteMapper;
import com.pes.jd.model.DO.LostRecordNote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * ClassName:LostRecordNoteDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:41:09 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository
public class LostRecordNoteDaoImpl implements LostRecordNoteDao {

	@Autowired
	private LostRecordNoteMapper lostRecordNoteMapper;

	@Override
	public int deleteLostRecordNoteById(Long id) {
		return lostRecordNoteMapper.deleteLostRecordNoteById(id);
	}

	@Override
	public int insertLostRecordNote(LostRecordNote record) {
		return lostRecordNoteMapper.insertLostRecordNote(record);
	}

	@Override
	public LostRecordNote getLostRecordNoteById(Long id) {
		return lostRecordNoteMapper.getLostRecordNoteById(id);
	}

	@Override
	public int updateLostRecordNoteBySelective(LostRecordNote record) {
		return lostRecordNoteMapper.updateLostRecordNoteBySelective(record);
	}

}
