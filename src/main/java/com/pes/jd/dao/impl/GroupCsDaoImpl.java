package com.pes.jd.dao.impl;

import com.pes.jd.dao.GroupCsDao;
import com.pes.jd.mapper.GroupCsMapper;
import com.pes.jd.model.DO.GroupCs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ClassName:GroupCsDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:22:11 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository
public class GroupCsDaoImpl implements GroupCsDao {

	@Autowired
	private GroupCsMapper groupCsMapper;

	@Override
	public int deleteGroupCsByGroupId(Long groupId) {
		return groupCsMapper.deleteGroupCsByGroupId(groupId);
	}

	@Override
	public int insertGroupCs(GroupCs record) {
		return groupCsMapper.insertGroupCs(record);
	}

	@Override
	public List<GroupCs> selectGroupCsByGroupId(GroupCsDao key) {
		return groupCsMapper.selectGroupCsByGroupId(key);
	}

	@Override
	public int updateGroupCsBySelective(GroupCs record) {
		return groupCsMapper.updateGroupCsBySelective(record);
	}

}
