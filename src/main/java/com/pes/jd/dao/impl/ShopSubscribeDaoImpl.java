package com.pes.jd.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.pes.jd.dao.ShopSubcribeDao;
import com.pes.jd.mapper.ShopSubcribeMapper;
import com.pes.jd.model.DO.ShopSubcribeDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;

@Repository
public class ShopSubscribeDaoImpl implements ShopSubcribeDao {

	@Autowired
	private ShopSubcribeMapper shopSubcribeMapper;

	@Override
	public int insertShopSubcribe(ShopSubcribeDO shopSubcribeDO,JobShopDTO shop) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_SUBCRIBE.getName());
		return shopSubcribeMapper.insert(shopSubcribeDO,tableName);
	}

	@Override
	public int deleteShopSubcribe(JobShopDTO shop) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_SUBCRIBE.getName());
		return shopSubcribeMapper.deleteShopSubcribe(shop.getShopId(),tableName);
	}

	@Override
	public int batchUpdateShopSubscribe(ShopSubcribeDO shopSubcribeDO, JobShopDTO shop) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_SUBCRIBE.getName());
		return shopSubcribeMapper.update(shopSubcribeDO,tableName);
	}

	@Override
	public List<ShopSubcribeDO> selectShopSubcribeByShopLstByDate(JobShopDTO shop, Date startDate, Date endDate) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_SUBCRIBE.getName());
		return shopSubcribeMapper.selectByShopId(shop.getShopId(), tableName);
	}

	
}
