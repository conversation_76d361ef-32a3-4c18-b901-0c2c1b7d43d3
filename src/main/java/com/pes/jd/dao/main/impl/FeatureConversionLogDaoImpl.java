package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.FeatureConversionLogDao;
import com.pes.jd.mapper.main.FeatureConversionLogMapper;
import com.pes.jd.model.DO.FeatureConversionLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository("featureConversionLogDao")
public class FeatureConversionLogDaoImpl implements FeatureConversionLogDao {

    private static final Logger logger = LoggerFactory.getLogger(FeatureConversionLogDaoImpl.class);

    @Resource
    private FeatureConversionLogMapper featureConversionLogMapper;

    @Override
    public int insert(FeatureConversionLog featureConversionLog) {
        return featureConversionLogMapper.insert(featureConversionLog);
    }
}