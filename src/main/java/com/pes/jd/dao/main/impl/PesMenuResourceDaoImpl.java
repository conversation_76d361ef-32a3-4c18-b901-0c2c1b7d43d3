package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.PesMenuResourceDao;
import com.pes.jd.mapper.main.PesMenuResourceMapper;
import com.pes.jd.model.DO.PesMenuResource;
import com.pes.jd.util.BaseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/14 5:20 PM
 * @since 1.0.0
 */
@Repository
public class PesMenuResourceDaoImpl implements PesMenuResourceDao {
    @Autowired
    private PesMenuResourceMapper pesMenuResourceMapper;
    @Override
    public int deleteByName(String name) {
        return pesMenuResourceMapper.deleteByName(name);
    }

    @Override
    public int insert(PesMenuResource record) {
        return pesMenuResourceMapper.insert(record);
    }

    @Override
    public List<PesMenuResource> selectByName(String name) {
        return pesMenuResourceMapper.selectByName(name);
    }

    @Override
    public List<PesMenuResource> selectByNameTitle(String name, String title) {
        return pesMenuResourceMapper.selectByNameTitle(name,title);
    }

    @Override
    public int updateByPrimaryKeySelective(PesMenuResource record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(PesMenuResource record) {
        return pesMenuResourceMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<PesMenuResource> searchAll(List<Long> menuIds, Long shopId, Integer type) {
        return BaseUtils.doWithSubList(menuIds,(menu)->
                pesMenuResourceMapper.searchAll(menu,shopId, type),200,true);
    }

    @Override
    public List<PesMenuResource> searchAllForSelf(List<Long> menuIds, Long shopId) {
        return BaseUtils.doWithSubList(menuIds,(menu)->
                pesMenuResourceMapper.searchAllForSelf(menu,shopId),200,true);
    }

    @Override
    public List<PesMenuResource> searchPerson() {
        return pesMenuResourceMapper.searchPerson();
    }

    @Override
    public Integer getMaxSort(Long parentId) {
        return pesMenuResourceMapper.getMaxSort(parentId);
    }

    @Override
    public int updatePermission(PesMenuResource pesMenuResource) {
        return pesMenuResourceMapper.updatePermission(pesMenuResource);
    }

    @Override
    public long selectSystemJurisdicteIdByTitle(String menuTitle, long shopId) {
        return pesMenuResourceMapper.selectSystemJurisdicteIdByTitle(menuTitle, shopId);
    }

    @Override
    public List<PesMenuResource> queryMenuByIds(List<Long> ids) {
        return pesMenuResourceMapper.queryMenuByIds(ids);
    }
}
