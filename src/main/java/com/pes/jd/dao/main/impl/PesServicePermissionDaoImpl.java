package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.PesServicePermissionDao;
import com.pes.jd.mapper.main.PesServicePermissionMapper;
import com.pes.jd.model.DO.PesServicePermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/14 5:27 PM
 * @since 1.0.0
 */
@Repository
public class PesServicePermissionDaoImpl implements PesServicePermissionDao {
    @Autowired
    private PesServicePermissionMapper pesServicePermissionMapper;

    @Override
    public List<PesServicePermission> searchAll(List<Long> servicePermissionIds, List<String> titles) {
        return pesServicePermissionMapper.searchAll(servicePermissionIds,titles);
    }
}
