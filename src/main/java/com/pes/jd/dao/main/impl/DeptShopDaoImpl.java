package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.DeptShopDao;
import com.pes.jd.mapper.main.DeptShopMapper;
import com.pes.jd.model.DO.DeptShopDO;
import com.pes.jd.model.DTO.BoardMnoitorParamDTO;
import com.pes.jd.ms.domain.Data.master.DeptShop;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Repository
public class DeptShopDaoImpl implements DeptShopDao {
    @Resource
    private DeptShopMapper deptShopMapper;
	@Override
	public	List<DeptShop> selectDeptShopInfoByDeptId(String shopName,Long deptId,List<Long> shpoIds){
		return deptShopMapper.selectDeptShopInfoByDeptId(shopName,deptId,shpoIds);
	}
    @Override
    public List<BoardMnoitorParamDTO> selectShopByDeptId(String deptId, String queryParam, Integer currentPage, Integer size, Map<String, Object> retMap) {
        List<BoardMnoitorParamDTO> shops;
        int count = deptShopMapper.selectShopNumsByDeptIdAndTitle(deptId, queryParam);
        //----查看所有的店铺
        Integer startIndex=0;
        if(null!=currentPage && !"0".equals(currentPage) && null !=size && size !=0){
            startIndex=(currentPage-1)*size;
        }
        shops = deptShopMapper.selectShopByDeptIdAndTitle(deptId, queryParam,startIndex,size);
        retMap.put("totalNum",count);
        return shops;
    }
    //判断字符串是不是以1-9开头的全数字
    public static boolean isNumber(String str) {
        String  regEx= "^[1-9]\\d{1,}$";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

	@Override
	public List<DeptShopDO> selectAllByDeptId(Integer deptId) {
		return deptShopMapper.selectAllByDeptId(deptId);
	}
    
}
