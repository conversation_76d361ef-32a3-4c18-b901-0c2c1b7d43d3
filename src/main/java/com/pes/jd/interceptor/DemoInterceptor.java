  
package com.pes.jd.interceptor;

import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**  
 * ClassName:DemoInterceptor <br/>  
 * Function: 自定义拦截器 <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月17日 下午3:30:49 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see
 */
public class DemoInterceptor implements HandlerInterceptor{

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object arg2,
			Exception e) throws Exception {
		  
		//只有返回true才会继续向下执行，返回false取消当前请求
//        System.out.println("Interceptor afterCompletion");
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object arg2,
			ModelAndView arg3) throws Exception {
		  
//		 System.out.println("Interceptor posthandler");
		
	}
	
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object arg2)
			throws Exception {
		 
//		 System.out.println("Interceptor preHandle");
		 return true;
	}

}
  
