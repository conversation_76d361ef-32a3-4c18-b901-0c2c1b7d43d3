  
package com.pes.jd.model.Enum;  
/**  
 * 接待类型
 * ClassName:ReciveTypeEnum <br/>  
 * Date:     2018年7月13日 下午2:04:54 <br/>  
 * <AUTHOR>
 */
public enum ReceiveTypeEnum {
	
	
	NOTCHAT("1","空聊天过滤"),
	ACCOUNTFILTER("2","自家账号过滤"),
	SYSCSFILTER("3","绩效软件客服聊天过滤"),
	CSFORWARDFILTER("4","客服转发过滤"),
	CSOFFLINEFILTER("5","客服离线消息过滤"),
	AUTOREPLYFILTER("6","主号自动回复过滤"),
	BUYERFILTER("7","指定顾客过滤"),
	WATCHWORD("8","暗语过滤"),
	CSSINGLECHATFILTER("9","客服单口相声过滤"),
	CUSTSINGLECHATFILTER("10","顾客单句过滤");
	
	private String type;
	private String name;
	private ReceiveTypeEnum(String type, String name) {
		this.type = type;
		this.name = name;
	}

	public String getType() {
		return type;
	}
	
	public String getName() {
		return name;
	}

	public static String getNameByType(String type) {
		for (ReceiveTypeEnum ele : ReceiveTypeEnum.values()) {
			if(ele.type.equals(type)){
				return ele.name;
			}
		}
		return "";
	}

}
  
