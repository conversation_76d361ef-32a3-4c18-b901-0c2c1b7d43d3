package com.pes.jd.model.Enum;

/**
 * 中差评类型
 * <AUTHOR>
 *
 */
public enum EvaluationTypeEnum {
	NeutralRates("neutralRates","neutral"), 
	BadRates("badRates","bad"),
	GoodRates("goodRates","good");
	
	private String name;
	private String type;
	
	EvaluationTypeEnum(String name, String type) {
		this.name = name;
		this.type = type;
	}
	
	public static String getType(String name){
		for (EvaluationTypeEnum evaluation : EvaluationTypeEnum.values()) {
			if(evaluation.getName().equals(name)){
				return evaluation.getType();
			}
		}
		return null;
	}
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
}
