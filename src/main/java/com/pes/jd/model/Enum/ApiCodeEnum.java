package com.pes.jd.model.Enum;

import com.pes.jd.constants.APICodeConstants;

/**
 * 前两个字符是 模块的拼音首字符 例如SY - 首页，
 * 接着后面两个字符是子模块 00,01
 * 最后连个字符是具体的错误码 递增
 * ClassName: ApiResponseCodeEnum <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON(可选). <br/>
 * date: 2018年10月24日 下午3:07:02 <br/>
 *
 * <AUTHOR>
 * @since JDK 1.8
 */
public enum ApiCodeEnum {

    CODE_SUCCESS_1001(APICodeConstants.CODE_SUCCESS_1001, "查询成功"),
    CODE_SUCCESS_1002(APICodeConstants.CODE_SUCCESS_1002, "操作成功"),
    CODE_SUCCESS_1003(APICodeConstants.CODE_SUCCESS_1003, "%s"),
    CODE_SUCCESS_1004(APICodeConstants.CODE_SUCCESS_1004, "检验成功"),

    //	-------------------------------------------------------------------------//
    CODE_ERROR_1001(APICodeConstants.CODE_ERROR_1001, "参数异常..."),
    CODE_ERROR_1002(APICodeConstants.CODE_ERROR_1002, "查询异常..."),
    CODE_ERROR_1003(APICodeConstants.CODE_ERROR_1003, "操作异常..."),
    CODE_ERROR_1004(APICodeConstants.CODE_ERROR_1004, "店铺名称与店铺ID不匹配,请重新输入!"),
    CODE_ERROR_1005(APICodeConstants.CODE_ERROR_1004, "店铺ID不存在,请重新输入!"),
    CODE_ERROR_1006(APICodeConstants.CODE_ERROR_1005, "短信验证码输入错误!"),
    CODE_ERROR_1007(APICodeConstants.CODE_ERROR_1005, "请重新获取短信验证码!"),
    CODE_ERROR_1008(APICodeConstants.CODE_ERROR_1008, "请检查上传的商品格式!"),

    //	------------------------------error common-------------------------------------//
    CODE_ERROR_FALLBACK_USRM(APICodeConstants.CODE_ERROR_FALLBACK_USRM, "用户管理服务异常"),
    CODE_ERROR_FALLBACK_SUB(APICodeConstants.CODE_ERROR_FALLBACK_SUB, "SUB服务异常"),
    CODE_ERROR_FALLBACK_RT_SUB(APICodeConstants.CODE_ERROR_FALLBACK_RT_SUB, "RT_SUB服务异常"),
    CODE_ERROR_FALLBACK_SCHEDULER(APICodeConstants.CODE_ERROR_FALLBACK_SCHEDULER, "调度中心服务异常"),
    CODE_ERROR_FALLBACK_JOB(APICodeConstants.CODE_ERROR_FALLBACK_JOB, "每日JOB服务异常"),
    CODE_ERROR_FALLBACK_RT_JOB(APICodeConstants.CODE_ERROR_FALLBACK_RT_JOB, "实时JOB服务异常"),

    CODE_ERROR_COMMON_DEFAULT(APICodeConstants.CODE_ERROR_COMMON_DEFAULT, "请求超时，请稍后再试..."),
    CODE_ERROR_COMMON_01_01(APICodeConstants.CODE_ERROR_COMMON_01_01, "请求超时，请稍后再试..."),
    CODE_ERROR_COMMON_DEMOTE(APICodeConstants.CODE_ERROR_COMMON_DEMOTE, "common rest 服务降级...."),

    //	------------------------------error common-------------------------------------//
    CODE_ERROR_JCQ_SEND_TASK_JOB(APICodeConstants.CODE_ERROR_JCQ_SEND_TASK_JOB, "发送消息失败！"),
    CODE_ERROR_JCQ_SEND_DATE_PARSE(APICodeConstants.CODE_ERROR_JCQ_SEND_DATE_PARSE, "发送消息失败！"),

    //	------------------------------error shop-------------------------------------//
    CODE_ERROR_CLEAN_JOB_PULL_RECORD(APICodeConstants.CODE_ERROR_CLEAN_JOB_PULL_RECORD, "清除任务失败！"),
    CODE_ERROR_CLEAN_RECORD_DATE_PARSE(APICodeConstants.CODE_ERROR_CLEAN_RECORD_DATE_PARSE, "清除任务 格式化日期失败！"),
    CODE_ERROR_SEARCH_JOB_PULL_RECORD(APICodeConstants.CODE_ERROR_SEARCH_JOB_PULL_RECORD, "查询任务失败！"),
    CODE_ERROR_SEARCH_RT_JOB_PULL_RECORD(APICodeConstants.CODE_ERROR_SEARCH_JOB_PULL_RECORD, "查询实时任务失败！"),
    CODE_ERROR_SEARCH_PVUV_RECORD(APICodeConstants.CODE_ERROR_SEARCH_JOB_PULL_RECORD, "查询PVUV任务失败！"),
    CODE_ERROR_SEARCH_DUTY_RECORD(APICodeConstants.CODE_ERROR_SEARCH_JOB_PULL_RECORD, "查询值班记录任务失败！"),
    CODE_ERROR_SEARCH_RECORD_DATE_PARSE(APICodeConstants.CODE_ERROR_SEARCH_RECORD_DATE_PARSE, "查询任务 格式化日期失败！"),


//	------------------------------系统设置-------------------------------------//

    // 咚咚设置 - 模块
    CODE_ERROR_XS_01_01(APICodeConstants.CODE_ERROR_XS_01_01, "查询异常....."),
    CODE_ERROR_XS_01_02(APICodeConstants.CODE_ERROR_XS_01_02, "参数为空"),
    CODE_ERROR_XS_01_03(APICodeConstants.CODE_ERROR_XS_01_03, "您设置的售前客服数超过了订购版本的限制！最多可以设置%d个售前咚咚号。"),
    CODE_ERROR_XS_01_04(APICodeConstants.CODE_ERROR_XS_01_04, "试用用户最多只能设置20个售前咚咚号。"),
    CODE_ERROR_XS_01_05(APICodeConstants.CODE_ERROR_XS_01_05, "设置咚咚失败。"),
    CODE_ERROR_XS_01_06(APICodeConstants.CODE_ERROR_XS_01_06, "移动咚咚失败。"),
    CODE_ERROR_XS_01_07(APICodeConstants.CODE_ERROR_XS_01_07, "咚咚只能在单店铺中移动。"),
    CODE_ERROR_XS_01_08(APICodeConstants.CODE_ERROR_XS_01_08, "解析json格式失败!。"),
    CODE_ERROR_XS_01_09(APICodeConstants.CODE_ERROR_XS_01_09, "修改咚咚昵称失败。"),
    CODE_ERROR_XS_01_10(APICodeConstants.CODE_ERROR_XS_01_10, "删除咚咚失败。"),
    CODE_ERROR_XS_01_12(APICodeConstants.CODE_ERROR_XS_01_12, "子账号刷新失败，请稍后再试!"),
    CODE_ERROR_XS_01_13(APICodeConstants.CODE_ERROR_XS_01_13, "设置客服状态失败!"),
    CODE_ERROR_XS_01_15(APICodeConstants.CODE_ERROR_XS_01_15, "参数解析异常"),
    CODE_ERROR_XS_01_16(APICodeConstants.CODE_ERROR_XS_01_16, "您设置的客服数超过了订购版本的限制！最多可以设置%s个客服。"),
    CODE_ERROR_XS_01_17(APICodeConstants.CODE_ERROR_XS_01_17, "修改客服组失败"),
    CODE_ERROR_XS_01_18(APICodeConstants.CODE_ERROR_XS_01_18, "请选择客服组!"),

    // 咚咚组设置 - 模块请填写分组的名称!
    CODE_ERROR_XS_02_01(APICodeConstants.CODE_ERROR_XS_02_01, "查询异常....."),
    CODE_ERROR_XS_02_02(APICodeConstants.CODE_ERROR_XS_02_02, "请填写分组的名称!"),
    CODE_ERROR_XS_02_03(APICodeConstants.CODE_ERROR_XS_02_03, "咚咚组名字数超过20!"),
    CODE_ERROR_XS_02_04(APICodeConstants.CODE_ERROR_XS_02_04, "设置咚咚组失败!"),
    CODE_ERROR_XS_02_05(APICodeConstants.CODE_ERROR_XS_02_05, "删除咚咚组失败!"),
    CODE_ERROR_XS_02_06(APICodeConstants.CODE_ERROR_XS_02_06, "该分组名称已经存在!"),
    //绩效设置 - 模块
    CODE_ERROR_XS_03_01(APICodeConstants.CODE_ERROR_XS_03_01, "查询异常....."),
    CODE_ERROR_XS_03_02(APICodeConstants.CODE_ERROR_XS_03_02, "查看详情失败....."),
    CODE_ERROR_XS_03_03(APICodeConstants.CODE_ERROR_XS_03_03, "获取客服绩效失败....."),
    CODE_ERROR_XS_03_04(APICodeConstants.CODE_ERROR_XS_03_04, "获取店铺绩效失败....."),
    CODE_ERROR_XS_03_05(APICodeConstants.CODE_ERROR_XS_03_05, "获取店铺商品类目失败....."),
    CODE_ERROR_XS_03_06(APICodeConstants.CODE_ERROR_XS_03_06, "获取店铺商品失败....."),
    CODE_ERROR_XS_03_07(APICodeConstants.CODE_ERROR_XS_03_07, "获取询单和出库有效时长修改限制失败....."),

    //权限设置 - 模块

    CODE_ERROR_XS_04_01(APICodeConstants.CODE_ERROR_XS_04_01, "查询咚咚权限缺少参数"),
    CODE_ERROR_XS_04_02(APICodeConstants.CODE_ERROR_XS_04_02, "查询店铺系统权限设置失败"),
    CODE_ERROR_XS_04_03(APICodeConstants.CODE_ERROR_XS_04_03, "一键解除所有授权异常"),
    CODE_ERROR_XS_04_04(APICodeConstants.CODE_ERROR_XS_04_04, "一键解除所有授权失败"),
    CODE_ERROR_XS_04_05(APICodeConstants.CODE_ERROR_XS_04_05, "修改咚咚权限失败"),
    CODE_ERROR_XS_04_06(APICodeConstants.CODE_ERROR_XS_04_06, "修改咚咚权限异常"),
    CODE_ERROR_XS_04_07(APICodeConstants.CODE_ERROR_XS_04_07, "管理员设置失败"),
    CODE_ERROR_XS_04_08(APICodeConstants.CODE_ERROR_XS_04_08, "管理员设置异常"),
    CODE_ERROR_XS_04_09(APICodeConstants.CODE_ERROR_XS_04_09, "查询店铺子账号异常"),
    CODE_ERROR_XS_04_10(APICodeConstants.CODE_ERROR_XS_04_10, "查询异常....."),
    CODE_ERROR_XS_04_11(APICodeConstants.CODE_ERROR_XS_04_11, "查询异常....."),
    CODE_ERROR_XS_04_12(APICodeConstants.CODE_ERROR_XS_04_12, "查询异常....."),
    CODE_ERROR_XS_04_13(APICodeConstants.CODE_ERROR_XS_04_13, "查询异常....."),
    CODE_ERROR_XS_04_14(APICodeConstants.CODE_ERROR_XS_04_14, "查询异常....."),
    CODE_ERROR_XS_04_15(APICodeConstants.CODE_ERROR_XS_04_15, "查询异常....."),
    CODE_ERROR_XS_04_16(APICodeConstants.CODE_ERROR_XS_04_16, "查询异常....."),

    CODE_ERROR_XS_04_51(APICodeConstants.CODE_ERROR_XS_04_51, "添加商品过滤失败"),
    CODE_ERROR_XS_04_52(APICodeConstants.CODE_ERROR_XS_04_52, "商品信息已存在"),
    CODE_ERROR_XS_04_53(APICodeConstants.CODE_ERROR_XS_04_53, "删除商品过滤失败"),
    CODE_ERROR_XS_04_54(APICodeConstants.CODE_ERROR_XS_04_54, "绩效基本设置失败"),
    CODE_ERROR_XS_04_55(APICodeConstants.CODE_ERROR_XS_04_55, "绩效判定设置失败"),
    CODE_ERROR_XS_04_56(APICodeConstants.CODE_ERROR_XS_04_56, "接待设置失败"),
    CODE_ERROR_XS_04_57(APICodeConstants.CODE_ERROR_XS_04_57, "买家过滤添加失败"),
    CODE_ERROR_XS_04_58(APICodeConstants.CODE_ERROR_XS_04_58, "买家过滤删除失败"),
    CODE_ERROR_XS_04_59(APICodeConstants.CODE_ERROR_XS_04_59, "查询异常....."),
    CODE_ERROR_XS_04_60(APICodeConstants.CODE_ERROR_XS_04_60, "查询异常....."),
    CODE_ERROR_XS_04_61(APICodeConstants.CODE_ERROR_XS_04_61, "实时绩效设置失败"),


    //店铺设置 - 模块
    CODE_ERROR_XS_05_01(APICodeConstants.CODE_ERROR_XS_05_01, "未查询到店铺信息,请点击修改,完善店铺信息....."),
    CODE_ERROR_XS_05_02(APICodeConstants.CODE_ERROR_XS_05_02, "查询店铺信息异常....."),
    CODE_ERROR_XS_05_03(APICodeConstants.CODE_ERROR_XS_05_03, "修改店铺信息失败....."),
    CODE_ERROR_XS_05_04(APICodeConstants.CODE_ERROR_XS_05_04, "修改店铺信息异常....."),

    //店铺设置 - 店铺组模块
    CODE_ERROR_XS_06_01(APICodeConstants.CODE_ERROR_XS_06_01, "修改多店铺开关异常....."),
    CODE_ERROR_XS_06_02(APICodeConstants.CODE_ERROR_XS_06_02, "查询当前店铺的店铺组信息异常"),
    CODE_ERROR_XS_06_03(APICodeConstants.CODE_ERROR_XS_06_03, "创建店铺组执行异常"),
    CODE_ERROR_XS_06_04(APICodeConstants.CODE_ERROR_XS_06_04, "修改店铺组名称异常"),
    CODE_ERROR_XS_06_05(APICodeConstants.CODE_ERROR_XS_06_05, "删除店铺组执行异常"),
    CODE_ERROR_XS_06_06(APICodeConstants.CODE_ERROR_XS_06_06, "邀请店铺执行异常"),
    CODE_ERROR_XS_06_07(APICodeConstants.CODE_ERROR_XS_06_07, "移动店铺执行异常"),
    CODE_ERROR_XS_06_08(APICodeConstants.CODE_ERROR_XS_06_08, "同步店铺执行异常"),
    CODE_ERROR_XS_06_09(APICodeConstants.CODE_ERROR_XS_06_09, "查询店铺组详情异常"),
    CODE_ERROR_XS_06_10(APICodeConstants.CODE_ERROR_XS_06_10, "查询我加入的店铺组异常"),
    CODE_ERROR_XS_06_11(APICodeConstants.CODE_ERROR_XS_06_11, "查询店铺授权列表异常"),
    CODE_ERROR_XS_06_12(APICodeConstants.CODE_ERROR_XS_06_12, "店铺授权报表操作异常"),
    CODE_ERROR_XS_06_31(APICodeConstants.CODE_ERROR_XS_06_31, "多店铺组内成员互看开关异常"),

    //敏感词设置
    CODE_ERROR_XS_07_01(APICodeConstants.CODE_ERROR_XS_07_01, "敏感词操作失败"),
    CODE_ERROR_XS_07_02(APICodeConstants.CODE_ERROR_XS_07_02, "敏感词已经存在"),
    CODE_ERROR_XS_07_03(APICodeConstants.CODE_ERROR_XS_07_03, "敏感词查询异常"),
    CODE_ERROR_XS_07_04(APICodeConstants.CODE_ERROR_XS_07_04, "添加敏感词上限"),

    //白名单设置
    CODE_ERROR_XS_08_01(APICodeConstants.CODE_ERROR_XS_08_01, "白名单操作失败"),
    CODE_ERROR_XS_08_02(APICodeConstants.CODE_ERROR_XS_08_02, "白名单设置异常"),
    CODE_ERROR_XS_08_03(APICodeConstants.CODE_ERROR_XS_08_03, "白名单查询异常"),

    //版本控制

    CODE_ERROR_XS_09_01(APICodeConstants.CODE_ERROR_XS_09_01, "版本控制操作失败"),
    CODE_ERROR_XS_09_02(APICodeConstants.CODE_ERROR_XS_09_02, "版本控制设置异常"),
    CODE_ERROR_XS_09_03(APICodeConstants.CODE_ERROR_XS_09_03, "版本控制查询异常"),


    //-------------------  数据分析    ---------------------//
    //接待分析
    CODE_ERROR_SF_01_01(APICodeConstants.CODE_ERROR_SF_01_01, "接待过滤记录查询失败！"),
    CODE_ERROR_SF_01_02(APICodeConstants.CODE_ERROR_SF_01_02, "日期格式不正确"),
    CODE_ERROR_SF_01_03(APICodeConstants.CODE_ERROR_SF_01_03, "查询聊天对象失败"),
    CODE_ERROR_SF_01_04(APICodeConstants.CODE_ERROR_SF_01_04, "查询聊天内容失败"),

    CODE_ERROR_SF_02_01(APICodeConstants.CODE_ERROR_SF_02_01, "查询异常....."),
    CODE_ERROR_SF_02_02(APICodeConstants.CODE_ERROR_SF_02_02, "成交分析查询异常....."),
    CODE_ERROR_SF_02_15(APICodeConstants.CODE_ERROR_SF_02_15, "店铺销售分析查询异常....."),
    CODE_ERROR_SF_02_16(APICodeConstants.CODE_ERROR_SF_02_16, "客服销售分析查询异常....."),
    CODE_ERROR_SF_02_17(APICodeConstants.CODE_ERROR_SF_02_17, "静默销售分析查询异常....."),

    CODE_ERROR_SF_02_03(APICodeConstants.CODE_ERROR_SF_01_08, "商品咨询汇总查询异常....."),
    CODE_ERROR_SF_02_04(APICodeConstants.CODE_ERROR_SF_01_09, "商品咨询明细查询异常....."),
    CODE_ERROR_SF_02_05(APICodeConstants.CODE_ERROR_SF_01_10, "成交订单详情查询异常....."),
    CODE_ERROR_SF_02_06(APICodeConstants.CODE_ERROR_SF_01_11, "客服咨询分析查询异常....."),

    //流失分析
    CODE_ERROR_SF_03_20(APICodeConstants.CODE_ERROR_SF_03_20, "时间解析异常"),
    CODE_ERROR_SF_03_21(APICodeConstants.CODE_ERROR_SF_03_21, "时间选择超过范围，请缩小时间间隔"),
    CODE_ERROR_SF_03_22(APICodeConstants.CODE_ERROR_SF_03_22, "静默流失记录查询失败！"),
    CODE_ERROR_SF_03_23(APICodeConstants.CODE_ERROR_SF_03_23, "saveOrUpdateLostRecordNote执行异常"),
    CODE_ERROR_SF_03_24(APICodeConstants.CODE_ERROR_SF_03_24, "保存备注失败"),
    CODE_ERROR_SF_03_25(APICodeConstants.CODE_ERROR_SF_03_25, "询单流失查询失败"),
    CODE_ERROR_SF_03_26(APICodeConstants.CODE_ERROR_SF_03_26, "询单下单未付款流失查询失败"),
    CODE_ERROR_SF_03_27(APICodeConstants.CODE_ERROR_SF_03_27, "静默下单未付款流失查询失败"),


    //登录分析
    CODE_ERROR_SF_04_01(APICodeConstants.CODE_ERROR_SF_04_01, "登录分析总览查询参数传递异常"),
    CODE_ERROR_SF_04_02(APICodeConstants.CODE_ERROR_SF_04_02, "登录分析总览查询失败....."),
    CODE_ERROR_SF_04_03(APICodeConstants.CODE_ERROR_SF_04_03, "登录分析总览查询异常....."),
    CODE_ERROR_SF_04_04(APICodeConstants.CODE_ERROR_SF_04_04, "登录分析详情查询参数传递异常"),
    CODE_ERROR_SF_04_05(APICodeConstants.CODE_ERROR_SF_04_05, "登录分析详情查询失败....."),
    CODE_ERROR_SF_04_06(APICodeConstants.CODE_ERROR_SF_04_06, "登录分析详情查询异常....."),
    CODE_ERROR_SF_04_07(APICodeConstants.CODE_ERROR_SF_04_07, "登录分析操作详情查询异常....."),
    CODE_ERROR_SF_04_08(APICodeConstants.CODE_ERROR_SF_04_08, "登录分析总览时间解析异常"),
    CODE_ERROR_SF_04_09(APICodeConstants.CODE_ERROR_SF_04_09, "登录分析详情时间解析异常"),

    CODE_ERROR_SF_05_01(APICodeConstants.CODE_ERROR_SF_05_01, "查询异常....."),

    CODE_ERROR_SF_06_01(APICodeConstants.CODE_ERROR_SF_06_01, "查询异常....."),

    CODE_ERROR_SF_07_01(APICodeConstants.CODE_ERROR_SF_06_01, "查询异常....."),
    //预约订单分析
    CODE_ERROR_SF_09_01(APICodeConstants.CODE_ERROR_SF_09_01, "预约订单分析查询失败....."),
    //商品推荐分析
    CODE_ERROR_SF_08_01(APICodeConstants.CODE_ERROR_SF_08_01, "商品推荐汇总查询失败"),

    CODE_ERROR_SF_08_02(APICodeConstants.CODE_ERROR_SF_08_02, "参数为空。"),
    //协助服务分析
    CODE_ERROR_AS_01_01(APICodeConstants.CODE_ERROR_AS_01_01, "协助服务分析查询异常"),

    //中差评分析
    CODE_ERROR_SE_01_01(APICodeConstants.CODE_ERROR_SE_01_01, "中差评分析查询异常"),

    //追责
    CODE_ERROR_SE_01_02(APICodeConstants.CODE_ERROR_SE_01_02, "追责查询异常"),
    CODE_ERROR_SE_01_03(APICodeConstants.CODE_ERROR_SE_01_03, "聊天记录查询时间超过10天，请重新查询"),
    CODE_ERROR_SE_01_04(APICodeConstants.CODE_ERROR_SE_01_04, "聊天记录查询传参异常，缺少参数"),

    //满意率分析
    CODE_ERROR_MY_01_01(APICodeConstants.CODE_ERROR_MY_01_01, "满意率查询异常"),

    //接待未邀评分析
    CODE_ERROR_YP_01_01(APICodeConstants.CODE_ERROR_YP_01_01, "未邀评查询异常"),

    //-------------------  绩效中心    ---------------------//
    //	实时绩效
    CODE_ERROR_JZ_01_01(APICodeConstants.CODE_ERROR_JZ_01_01, "实时绩效拉取失败"),

    //店铺
    CODE_ERROR_JZ_02_01(APICodeConstants.CODE_ERROR_JZ_02_01, "店铺绩效查询异常....."),

    CODE_ERROR_JZ_02_02(APICodeConstants.CODE_ERROR_JZ_02_02, "店铺数据总览查询失败"),

    //客服咚咚绩效
    CODE_ERROR_JZ_03_01(APICodeConstants.CODE_ERROR_JZ_03_01, "查询异常....."),
    //首页index

    CODE_ERROR_IX_01_01(APICodeConstants.CODE_ERROR_IX_01_01, "首页快捷菜单列表查询失败"),
    CODE_ERROR_IX_01_02(APICodeConstants.CODE_ERROR_IX_01_02, "登录用户首页快捷菜单查询失败"),
    CODE_ERROR_IX_01_03(APICodeConstants.CODE_ERROR_IX_01_03, "保存首页快捷菜单失败"),
    //-------------------  首页&初始化   ---------------------//
    CODE_ERROR_SY_01_01(APICodeConstants.CODE_ERROR_SY_01_01, "查询异常....."),

    CODE_ERROR_SY_02_01(APICodeConstants.CODE_ERROR_SY_02_01, "查询异常....."),

    //-------------------  发送mq消息   ---------------------//
    CODE_ERROR_MQ_01_01(APICodeConstants.CODE_ERROR_MQ_01_01, " mq信息发送失败 "),

    //-------------------  后台登录   ---------------------//
    CODE_ERROR_HD_01_01(APICodeConstants.CODE_ERROR_HD_01_01, "密码不能为空"),
    CODE_ERROR_HD_01_02(APICodeConstants.CODE_ERROR_HD_01_02, "密码验证失败"),
    CODE_ERROR_HD_01_03(APICodeConstants.CODE_ERROR_HD_01_03, "test_ok"),//测试账号登录
    CODE_ERROR_HD_01_04(APICodeConstants.CODE_ERROR_HD_01_04, "模糊查询店铺信息异常"),
    CODE_ERROR_HD_01_05(APICodeConstants.CODE_ERROR_HD_01_05, "管理员登录当前店铺失败"),
    CODE_ERROR_HD_01_06(APICodeConstants.CODE_ERROR_HD_01_06, "加载当前店铺资源失败"),
    CODE_ERROR_HD_01_07(APICodeConstants.CODE_ERROR_HD_01_07, "请输入查询条件"),
    CODE_ERROR_HD_01_13(APICodeConstants.CODE_ERROR_HD_01_13, "......"),
    CODE_ERROR_HD_01_14(APICodeConstants.CODE_ERROR_HD_01_14,"登录超时"),

    // job日志
    CODE_ERROR_JOB_01_01(APICodeConstants.CODE_ERROR_JOB_01_01, "job日志插入失败"),

    // 手动拉取数据-重算数据-初始话用户数据
    CODE_ERROR_JOB_TASK_01_01(APICodeConstants.CODE_ERROR_JOB_TASK_01_01, "(手动拉取数据-重算数据-初始话用户数据) -> 失败"),
    //登录初始化模块
    CODE_ERROR_DL_01_01(APICodeConstants.CODE_ERROR_DL_01_01, "未查询到店铺信息，请尝试重新登录"),

    CODE_ERROR_DL_01_02(APICodeConstants.CODE_ERROR_DL_01_02, "初始化任务发送失败（MQ异常）"),
    
    CODE_ERROR_DL_01_03(APICodeConstants.CODE_ERROR_DL_01_03, "获取店铺初始化状态失败！"),
    
	CODE_ERROR_DL_01_04(APICodeConstants.CODE_ERROR_DL_01_04, "切换店铺失败"),
	
	CODE_ERROR_DL_01_05(APICodeConstants.CODE_ERROR_DL_01_05, "时间格式化错误"),
	
	CODE_ERROR_DL_01_06(APICodeConstants.CODE_ERROR_DL_01_06, ""),
	
	CODE_ERROR_DL_01_07(APICodeConstants.CODE_ERROR_DL_01_07, "该店铺订购已过期"),
	

	CODE_ERROR_DL_01_08(APICodeConstants.CODE_ERROR_DL_01_08, "参数为空"),
	CODE_ERROR_DL_01_09(APICodeConstants.CODE_ERROR_DL_01_09, "获取店铺信息失败"),
	CODE_ERROR_DL_01_10(APICodeConstants.CODE_ERROR_DL_01_10, "切换店铺,获取用户信息失败"),

    // 用户日志 - 模块
    CODE_ERROR_UL_01_01(APICodeConstants.CODE_ERROR_UL_01_01, " 用户日志查询失败 "),
    // 权限设置
    CODE_ERROR_PM_01_01(APICodeConstants.CODE_ERROR_PM_01_01, " 权限操作失败 "),
    CODE_ERROR_PM_01_03(APICodeConstants.CODE_ERROR_PM_01_01, " 找回密码失败 "),
    CODE_ERROR_PM_01_02(APICodeConstants.CODE_ERROR_PM_01_02, " 管理员设置下拉列表查询失败 "),

    //商品
    CODE_ERROR_SP_01_01(APICodeConstants.CODE_ERROR_SP_01_01, "获取店铺SKU商品失败....."),
    CODE_ERROR_SP_01_02(APICodeConstants.CODE_ERROR_SP_01_01, "删除商品知识库失败"),
    CODE_ERROR_SP_01_03(APICodeConstants.CODE_ERROR_SP_01_01, "更新商品知识库失败"),
    CODE_ERROR_SP_01_04(APICodeConstants.CODE_ERROR_SP_01_01, "插入商品知识库失败"),

    CODE_ERROR_SP_01_05(APICodeConstants.CODE_ERROR_SP_01_01, "设置主推失败"),
    CODE_ERROR_SP_01_06(APICodeConstants.CODE_ERROR_SP_01_01, "取消主推失败"),
    CODE_ERROR_SP_01_07(APICodeConstants.CODE_ERROR_SP_01_01, "设置关联商品失败"),

    //实时绩效
    CODE_ERROR_RT_01_01(APICodeConstants.CODE_ERROR_RT_01_01, "未获取用户信息，请重新登录....."),
    CODE_ERROR_RT_03_03(APICodeConstants.CODE_ERROR_RT_03_03, "%s客服已选,请重新选择"),
    CODE_ERROR_RT_01_03(APICodeConstants.CODE_ERROR_RT_01_03, "店铺信息为空"),
    CODE_ERROR_RT_01_04(APICodeConstants.CODE_ERROR_RT_01_04, "获取店铺信息为空"),
    CODE_ERROR_RT_01_05(APICodeConstants.CODE_ERROR_RT_01_05, "参数为空"),
    CODE_ERROR_RT_01_06(APICodeConstants.CODE_ERROR_RT_01_06, "查询话术失败"),
    CODE_ERROR_RT_03_07(APICodeConstants.CODE_ERROR_RT_03_07, "该顾客已付款无需提醒"),
    CODE_ERROR_RT_03_09(APICodeConstants.CODE_ERROR_RT_03_09, "该顾客已取消无需提醒"),
    //实时绩效-实时监控
    CODE_ERROR_RT_06_01(APICodeConstants.CODE_ERROR_RT_06_01, "web-实时监控-不良接待查询异常"),
    CODE_ERROR_RT_06_02(APICodeConstants.CODE_ERROR_RT_06_02, "web-实时监控-坐席监控查询异常"),
    CODE_ERROR_RT_06_03(APICodeConstants.CODE_ERROR_RT_06_03, "web-实时监控-全局监控查询异常"),
    CODE_ERROR_RT_06_04(APICodeConstants.CODE_ERROR_RT_06_04, "web-实时监控-查询告警设置失败"),
    CODE_ERROR_RT_06_05(APICodeConstants.CODE_ERROR_RT_06_05, "web-实时监控-修改告警设置失败"),

    CODE_ERROR_RT_06_06(APICodeConstants.CODE_ERROR_RT_06_06, "web-实时看板-全局监控明细查询异常"),

    //实时绩效-客服绩效
    CODE_ERROR_RT_07_01(APICodeConstants.CODE_ERROR_RT_07_01, "实时绩效-客服对比查询异常"),
    CODE_ERROR_RT_07_02(APICodeConstants.CODE_ERROR_RT_07_02, "实时绩效-分时绩效查询异常"),
    CODE_ERROR_RT_07_03(APICodeConstants.CODE_ERROR_RT_07_03, "实时绩效-绩效明细查询异常"),
    // 自定义报表
    CODE_ERROR_CR_01_01(APICodeConstants.CODE_ERROR_CR_01_01, "自定义报表操作失败"),

    //导出任务
    CODE_ERROR_EX_01_01(APICodeConstants.CODE_ERROR_EX_01_01, "导出失败"),
    CODE_ERROR_EX_01_02(APICodeConstants.CODE_ERROR_EX_01_02, "更新导出记录的文件url失败"),
    CODE_ERROR_EX_01_03(APICodeConstants.CODE_ERROR_EX_01_03, "删除导出记录失败"),
    CODE_ERROR_EX_02_04(APICodeConstants.CODE_ERROR_EX_02_04, "查询导出记录失败"),
    CODE_ERROR_YF_01_01(APICodeConstants.CODE_ERROR_YF_01_01, "意见反馈插入失败"),

    //店铺订购
	CODE_ERROR_DG_01_01(APICodeConstants.CODE_ERROR_DG_01_01, "拉取订购信息失败"),
	CODE_ERROR_SD_01_01(APICodeConstants.CODE_ERROR_SD_01_01, "查询店铺订购信息失败"),

	//实时看板
	CODE_ERROR_SK_01_01(APICodeConstants.CODE_ERROR_SK_01_01, "查询异常"),
    //自动分配设置
    CODE_ERROR_AAS_01_01(APICodeConstants.CODE_ERROR_AAS_01_01, "请选择客服!"),
    CODE_ERROR_AAS_01_02(APICodeConstants.CODE_ERROR_AAS_01_02, "店铺自动分配设置失败"),
    CODE_ERROR_AAS_01_03(APICodeConstants.CODE_ERROR_AAS_01_03, "查询失败"),
    CODE_ERROR_AAS_01_04(APICodeConstants.CODE_ERROR_AAS_01_04, "店铺ID为空!"),
    CODE_ERROR_CS_01_01(APICodeConstants.CODE_ERROR_CS_01_01, "查询在线客服异常!"),


    CODE_ERROR_TF_01_01(APICodeConstants.CODE_ERROR_TF_01_01, "查询任务分配提醒数量异常"),

    CODE_ERROR_SU_01_01(APICodeConstants.CODE_ERROR_SU_01_01, "查询催付店铺异常"),
    CODE_ERROR_SU_01_02(APICodeConstants.CODE_ERROR_SU_01_02, "查询催付信息异常"),

    CODE_ERROR_BR_01_01(APICodeConstants.CODE_ERROR_BR_01_01, "请选择兜底客服!"),
    CODE_ERROR_BR_01_02(APICodeConstants.CODE_ERROR_BR_01_02, "请选择指定客服!"),
    CODE_ERROR_BR_01_03(APICodeConstants.CODE_ERROR_XS_04_62, "上传文件有误!"),

    //短信话术发送设置
    CODE_ERROR_SW_01_01(APICodeConstants.CODE_ERROR_SW_01_01, "短信话术设置失败"),
    CODE_ERROR_SW_01_02(APICodeConstants.CODE_ERROR_SW_01_02, "删除短息话术失败"),
    CODE_ERROR_SW_01_03(APICodeConstants.CODE_ERROR_SW_01_03, "查询话术失败"),
    CODE_ERROR_SW_01_04(APICodeConstants.CODE_ERROR_SW_01_04, "店铺短信设置失败"),
    CODE_ERROR_SW_01_05(APICodeConstants.CODE_ERROR_SW_01_05, "查询店铺短信信息失败"),
    CODE_ERROR_SW_01_06(APICodeConstants.CODE_ERROR_SW_01_06, "移除黑名单失败"),
    CODE_ERROR_SW_01_07(APICodeConstants.CODE_ERROR_SW_01_07, "增加黑名单失败"),
    CODE_ERROR_SW_01_08(APICodeConstants.CODE_ERROR_SW_01_08, "查询黑名单信息失败"),
    CODE_ERROR_SW_01_09(APICodeConstants.CODE_ERROR_SW_01_09, "参数为空"),

    CODE_ERROR_CBR_01_01(APICodeConstants.CODE_ERROR_CBR_01_01, "设置咨询未下单催拍失败"),
    CODE_ERROR_CBR_01_02(APICodeConstants.CODE_ERROR_CBR_01_02, "查询咨询未下单催拍信息失败"),
    CODE_ERROR_CBR_01_03(APICodeConstants.CODE_ERROR_CBR_01_03, "参数不能为空"),

    //短信充值模块
    CODE_ERROR_SMS_01_01(APICodeConstants.CODE_ERROR_SMS_01_01, "短信充值失败..."),
    CODE_ERROR_SMS_01_02(APICodeConstants.CODE_ERROR_SMS_01_02, "查询短信充值记录失败..."),
    //支付模块
    CODE_ERROR_ZF_01_01(APICodeConstants.CODE_ERROR_ZF_01_01, "订单状态校验失败..."),

    //全局异常处理返回设置
    CODE_ERROR_QJ_01_01(APICodeConstants.CODE_ERROR_QJ_01_01, "系统处理失败,请重新登录!"),

    CODE_ERROR_KB_00_00(APICodeConstants.CODE_ERROR_KB_00_00, "查询参数异常!"),
    CODE_ERROR_YY_01_01(APICodeConstants.CODE_ERROR_YY_01_01, "预约活动店铺绩效查询失败！"),
    CODE_ERROR_YY_01_02(APICodeConstants.CODE_ERROR_YY_01_02, "预约活动汇总查询失败！"),
    CODE_ERROR_SG_01_01(APICodeConstants.CODE_ERROR_SG_01_01, "查询异常!"),
    CODE_ERROR_SG_01_02(APICodeConstants.CODE_ERROR_SG_01_02, "参数为空!"),
    CODE_ERROR_SG_01_03(APICodeConstants.CODE_ERROR_SG_01_03, "时间格式不正确，请传YYYY-MM-dd HH:mm:ss格式"),
    CODE_ERROR_SC_01_01(APICodeConstants.CODE_ERROR_SC_01_01, "发送优惠券失败!"),
    CODE_ERROR_SC_01_02(APICodeConstants.CODE_ERROR_SC_01_02, ""),
    CODE_ERROR_SC_01_03(APICodeConstants.CODE_ERROR_SC_01_03, "发送话术失败!"),

    CODE_ERROR_PHONE_01_01(APICodeConstants.CODE_ERROR_PHONE_01_01, "查询失败，请再试一次"),
    CODE_ERROR_PHONE_01_02(APICodeConstants.CODE_ERROR_PHONE_01_02, "今日查看次数已达上限");
    private String code;//错误码
    private String msg;//错误信息

    private ApiCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
