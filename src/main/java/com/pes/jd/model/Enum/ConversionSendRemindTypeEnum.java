package com.pes.jd.model.Enum;

public enum ConversionSendRemindTypeEnum {

//发送提醒方式 0：默认值 1:咚咚提醒类型 2：短信提醒类型 3:全部
	DEFAULT(0,"默认值"),
	DONGDONG(1,"咚咚提醒类型"),
	SMS(2,"短信提醒类型"),
	DONGDONG_SMS(3,"全部");
	private Integer type;
	private String name;
	ConversionSendRemindTypeEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	public Integer getType() {
		return type;
	}
	
	public String getName() {
		return name;
	}

	
}
