
package com.pes.jd.model.Enum;  
/**  
 * 绩效明细数据指标
 */
public enum CsPerformanceDetailEnum {
	
	RECEIVE_DETAIL("1","接待明细"),
	SALE_DETAIL("2","销售明细"),
	ENQUIRY_DETAIL("3","询单明细"),
	ENQUIRY_ORDERD_UNPAY_DETAIL("4","询单下单未付款明细"),
	ENQUIRY_UNORDERD_DETAIL("5","询单未下单明细"),
	NON_REPLY_DETAIL("6","未回复明细"),
	RECEIVE_FILTER_DETAIL("7","接待过滤明细");
	private String type;
	private String name;
	private CsPerformanceDetailEnum(String type, String name) {
		this.type = type;
		this.name = name;
	}

	public String getType() {
		return type;
	}
	
	public String getName() {
		return name;
	}

	public static String getNameByType(String type) {
		for (CsPerformanceDetailEnum ele : CsPerformanceDetailEnum.values()) {
			if(ele.type.equals(type)){
				return ele.name;
			}
		}
		return "";
	}

}
  
