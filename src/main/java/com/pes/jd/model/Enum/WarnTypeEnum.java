package com.pes.jd.model.Enum;

import java.text.MessageFormat;

public enum WarnTypeEnum {
	WARN_TYPE_LOGIN("1","通知解卦","【{0}】客服，请解除挂起，上线接待"),
	WARN_TYPE_HANGUP("2","通知挂起","【{0}】客服，请将咚咚挂起"),
	WARN_TYPE_CS_VIOLATION("3","不良接待-客服违规","【{0}】客服有违规行为，请登录客服魔方「实时监控」查看"),
	WARN_TYPE_CUSTOMER_RAIL("4","不良接待-顾客辱骂","【{0}】客服接待时，【{1}】顾客有辱骂行为，请登录客服魔方「实时监控」查看"),
	WARN_TYPE_SLOW_RESPONSE("5","不良接待-慢响应","【{0}】客服接待达到慢响应指标，请登录客服魔方「实时监控」查看");
	//customer
	private String type;//告警类型
	private String title;//告警标题
	private String content;//告警内容
	
	private WarnTypeEnum(String type,String title, String content){
		this.type = type;
		this.title = title;
		this.content = content;
	}

	public static String getContentByWarnType(String type,String csNick,String customer){
		for (WarnTypeEnum ele : WarnTypeEnum.values()) {
			if(ele.type.equals(type)){
				return MessageFormat.format(ele.content, csNick, customer);
			}
		}
		return "";
	}
	
	public static String getTitleByWarnType(String type){
		for (WarnTypeEnum ele : WarnTypeEnum.values()) {
			if(ele.type.equals(type)){
				return ele.title;
			}
		}
		return "";
	}

	public String getType() {
		return type;
	}

	public String getTitle() {
		return title;
	}

	public String getContent() {
		return content;
	}
	
}
  
