  
package com.pes.jd.model.Enum;  
/**  
 * 接待类型
 * ClassName:ReciveTypeEnum <br/>  
 * Date:     2018年7月13日 下午2:04:54 <br/>  
 * <AUTHOR>
 */
public enum ReciveTypeEnum {
	
	CONSULT("1","咨询"),RECEIVE("2","接待"),ENQUIRY("3","询单"),ASSIST("4","协助服务"),AFTERSALE("5","售后"),
	TEAMCHAT("6","团队内部"),BUYERFILTER("7","买家过滤"),WATCHWORD("8","暗语过滤"),SINGLECHATFILTER("9","单口相声过滤");
	private String type;
	private String name;
	private ReciveTypeEnum(String type, String name) {
		this.type = type;
		this.name = name;
	}

	public String getType() {
		return type;
	}
	
	public String getName() {
		return name;
	}

	public static String getNameByType(String type) {
		for (ReciveTypeEnum ele : ReciveTypeEnum.values()) {
			if(ele.type.equals(type)){
				return ele.name;
			}
		}
		return "";
	}

}
  
