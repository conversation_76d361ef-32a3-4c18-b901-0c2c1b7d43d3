  
package com.pes.jd.model.Enum;

/**
 * 待转换池备注类型
 * ConvertOrderTypeEnum <br/>  
 */
public enum ConvertRemarkTypeEnum {

	ORDERED(1,"已下单"),
	PAYMENT(2,"已付款"),
	CANCELED(3,"已取消"),
	CHATSESSION_CONSULT(4,"正在会话中 "),
	VALID(5,"无效"),
	SEND_NUM_REACHED_LIMIT(6,"发送次数达到上线"),
	SKU_NOT_ENOUGH(7,"sku库存不够"),
	FORWARD_AFTERSALE_CS(8,"转发售后客服"),
	EXCEED_VALID_TIME(9,"超过有效期"),
	BARGAIN(10,"已付定金"),
	BALANCE(11,"已付尾款"),
	IGNORE(12,"已忽略"),
	RESERVED(13,"已预约");
	private Integer type;
	private String name;
	private ConvertRemarkTypeEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	public Integer getType() {
		return type;
	}
	
	public String getName() {
		return name;
	}

	public static String getNameByType(Integer type) {
		for (ConvertRemarkTypeEnum ele : ConvertRemarkTypeEnum.values()) {
			if(ele.type.equals(type)){
				return ele.name;
			}
		}
		return "";
	}
}
  
