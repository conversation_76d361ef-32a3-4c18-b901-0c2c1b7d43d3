package com.pes.jd.model.DO;

import java.util.Date;
import java.util.Objects;

public class CsLoginlog {
    private Long id;

    private String csNick;

    private Date loginTime;

    private Date logoutTime;

    private Long shopId;
    
    private String ip;//登录ip

    private String loginSid;//登录sid

	private Integer type;

	private Date changeTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public Date getLoginTime() {
		return loginTime;
	}

	public void setLoginTime(Date loginTime) {
		this.loginTime = loginTime;
	}

	public Date getLogoutTime() {
		return logoutTime;
	}

	public void setLogoutTime(Date logoutTime) {
		this.logoutTime = logoutTime;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getLoginSid() {
		return loginSid;
	}

	public void setLoginSid(String loginSid) {
		this.loginSid = loginSid;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Date getChangeTime() {
		return changeTime;
	}

	public void setChangeTime(Date changeTime) {
		this.changeTime = changeTime;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (!(o instanceof CsLoginlog)) return false;
		CsLoginlog that = (CsLoginlog) o;
		return Objects.equals(csNick, that.csNick) &&
				Objects.equals(shopId, that.shopId) &&
				Objects.equals(type, that.type) &&
				Objects.equals(changeTime, that.changeTime);
	}

	@Override
	public int hashCode() {
		return Objects.hash(csNick, shopId, type, changeTime);
	}
}