package com.pes.jd.model.DO;

import java.io.Serializable;

public class ShopSystemsetting implements Serializable {
	private Long id;

	private Long shopId;

	private Boolean autoReplySwitch;

	private String autoReplyMark;

	private Integer sellAfter;

	private Integer maxWaitTime;

	private Integer schedulingTimeDot;

	private String jixiaoTime;

	private Integer judgeRuleAscription;

	private Integer judgeRule;

	private Boolean silentAllSwitch;

	private Integer silentAllFollowUpTime;

	private Boolean isBindSilentAllOrder;

	private Integer minReplyNum;

	private Boolean activeFollowUpSwitch;

	private Boolean silentUrgepaySwitch;

	private Integer silentUrgepayTime;

	private Boolean bannerSwitch;

	private Long bannerFlag;

	private Boolean filterNonChat;

	private Boolean teamChatFilter;

	private Boolean sysCsChatFilter;

	private Boolean csWatchwordSwitch;

	private String csWatchword;

	private Boolean csSingleChatFilter;

	private Integer singleChatNum;

	private Boolean buyerChatlogWordSwitch;

	private Integer urgePayTime;

	private String urgeOrderWord;

	private String urgePayWord;

	private Boolean filterBuyerSigchat;

	private Boolean filterEserviceFwd;

	private Integer sigchatMinReplyNum;

	private Integer csFwdChatNum;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Boolean getAutoReplySwitch() {
		return autoReplySwitch;
	}

	public void setAutoReplySwitch(Boolean autoReplySwitch) {
		this.autoReplySwitch = autoReplySwitch;
	}

	public String getAutoReplyMark() {
		return autoReplyMark;
	}

	public void setAutoReplyMark(String autoReplyMark) {
		this.autoReplyMark = autoReplyMark;
	}

	public Integer getSellAfter() {
		return sellAfter;
	}

	public void setSellAfter(Integer sellAfter) {
		this.sellAfter = sellAfter;
	}

	public Integer getMaxWaitTime() {
		return maxWaitTime;
	}

	public void setMaxWaitTime(Integer maxWaitTime) {
		this.maxWaitTime = maxWaitTime;
	}

	public Integer getSchedulingTimeDot() {
		return schedulingTimeDot;
	}

	public void setSchedulingTimeDot(Integer schedulingTimeDot) {
		this.schedulingTimeDot = schedulingTimeDot;
	}

	public String getJixiaoTime() {
		return jixiaoTime;
	}

	public void setJixiaoTime(String jixiaoTime) {
		this.jixiaoTime = jixiaoTime;
	}

	public Integer getJudgeRuleAscription() {
		return judgeRuleAscription;
	}

	public void setJudgeRuleAscription(Integer judgeRuleAscription) {
		this.judgeRuleAscription = judgeRuleAscription;
	}

	public Integer getJudgeRule() {
		return judgeRule;
	}

	public void setJudgeRule(Integer judgeRule) {
		this.judgeRule = judgeRule;
	}

	public Boolean getSilentAllSwitch() {
		return silentAllSwitch;
	}

	public void setSilentAllSwitch(Boolean silentAllSwitch) {
		this.silentAllSwitch = silentAllSwitch;
	}

	public Integer getSilentAllFollowUpTime() {
		return silentAllFollowUpTime;
	}

	public void setSilentAllFollowUpTime(Integer silentAllFollowUpTime) {
		this.silentAllFollowUpTime = silentAllFollowUpTime;
	}

	public Boolean getIsBindSilentAllOrder() {
		return isBindSilentAllOrder;
	}

	public void setIsBindSilentAllOrder(Boolean isBindSilentAllOrder) {
		this.isBindSilentAllOrder = isBindSilentAllOrder;
	}

	public Integer getMinReplyNum() {
		return minReplyNum;
	}

	public void setMinReplyNum(Integer minReplyNum) {
		this.minReplyNum = minReplyNum;
	}

	public Boolean getActiveFollowUpSwitch() {
		return activeFollowUpSwitch;
	}

	public void setActiveFollowUpSwitch(Boolean activeFollowUpSwitch) {
		this.activeFollowUpSwitch = activeFollowUpSwitch;
	}

	public Boolean getSilentUrgepaySwitch() {
		return silentUrgepaySwitch;
	}

	public void setSilentUrgepaySwitch(Boolean silentUrgepaySwitch) {
		this.silentUrgepaySwitch = silentUrgepaySwitch;
	}

	public Integer getSilentUrgepayTime() {
		return silentUrgepayTime;
	}

	public void setSilentUrgepayTime(Integer silentUrgepayTime) {
		this.silentUrgepayTime = silentUrgepayTime;
	}

	public Boolean getBannerSwitch() {
		return bannerSwitch;
	}

	public void setBannerSwitch(Boolean bannerSwitch) {
		this.bannerSwitch = bannerSwitch;
	}

	public Long getBannerFlag() {
		return bannerFlag;
	}

	public void setBannerFlag(Long bannerFlag) {
		this.bannerFlag = bannerFlag;
	}

	public Boolean getFilterNonChat() {
		return filterNonChat;
	}

	public void setFilterNonChat(Boolean filterNonChat) {
		this.filterNonChat = filterNonChat;
	}

	public Boolean getTeamChatFilter() {
		return teamChatFilter;
	}

	public void setTeamChatFilter(Boolean teamChatFilter) {
		this.teamChatFilter = teamChatFilter;
	}

	public Boolean getSysCsChatFilter() {
		return sysCsChatFilter;
	}

	public void setSysCsChatFilter(Boolean sysCsChatFilter) {
		this.sysCsChatFilter = sysCsChatFilter;
	}

	public Boolean getCsWatchwordSwitch() {
		return csWatchwordSwitch;
	}

	public void setCsWatchwordSwitch(Boolean csWatchwordSwitch) {
		this.csWatchwordSwitch = csWatchwordSwitch;
	}

	public String getCsWatchword() {
		return csWatchword;
	}

	public void setCsWatchword(String csWatchword) {
		this.csWatchword = csWatchword;
	}

	public Boolean getCsSingleChatFilter() {
		return csSingleChatFilter;
	}

	public void setCsSingleChatFilter(Boolean csSingleChatFilter) {
		this.csSingleChatFilter = csSingleChatFilter;
	}

	public Integer getSingleChatNum() {
		return singleChatNum;
	}

	public void setSingleChatNum(Integer singleChatNum) {
		this.singleChatNum = singleChatNum;
	}

	public Boolean getBuyerChatlogWordSwitch() {
		return buyerChatlogWordSwitch;
	}

	public void setBuyerChatlogWordSwitch(Boolean buyerChatlogWordSwitch) {
		this.buyerChatlogWordSwitch = buyerChatlogWordSwitch;
	}

	public Integer getUrgePayTime() {
		return urgePayTime;
	}

	public void setUrgePayTime(Integer urgePayTime) {
		this.urgePayTime = urgePayTime;
	}

	public String getUrgeOrderWord() {
		return urgeOrderWord;
	}

	public void setUrgeOrderWord(String urgeOrderWord) {
		this.urgeOrderWord = urgeOrderWord;
	}

	public String getUrgePayWord() {
		return urgePayWord;
	}

	public void setUrgePayWord(String urgePayWord) {
		this.urgePayWord = urgePayWord;
	}

	public Boolean getFilterBuyerSigchat() {
		return filterBuyerSigchat;
	}

	public void setFilterBuyerSigchat(Boolean filterBuyerSigchat) {
		this.filterBuyerSigchat = filterBuyerSigchat;
	}

	public Boolean getFilterEserviceFwd() {
		return filterEserviceFwd;
	}

	public void setFilterEserviceFwd(Boolean filterEserviceFwd) {
		this.filterEserviceFwd = filterEserviceFwd;
	}

	public Integer getSigchatMinReplyNum() {
		return sigchatMinReplyNum;
	}

	public void setSigchatMinReplyNum(Integer sigchatMinReplyNum) {
		this.sigchatMinReplyNum = sigchatMinReplyNum;
	}

	public Integer getCsFwdChatNum() {
		return csFwdChatNum;
	}

	public void setCsFwdChatNum(Integer csFwdChatNum) {
		this.csFwdChatNum = csFwdChatNum;
	}

}