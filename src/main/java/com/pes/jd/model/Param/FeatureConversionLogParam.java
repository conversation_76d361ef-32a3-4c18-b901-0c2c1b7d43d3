package com.pes.jd.model.Param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 功能转化日志参数
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureConversionLogParam {
    
    /** 功能位置ID */
    private Integer featureLocationId;
    
    /** 店铺ID */
    private Long shopId;
    
    /** 客服昵称 */
    private String csNick;
    
    /** 操作类型：1展示，2转化，3关闭 */
    private Integer optType;
    
    /** 用户产品等级：1pop基础版，2pop高级版，3自营版 */
    private Integer userLevel;

    public FeatureConversionLogParam() {
        super();
    }

    public FeatureConversionLogParam(Integer featureLocationId, Long shopId, String csNick, Integer optType, Integer userLevel) {
        this.featureLocationId = featureLocationId;
        this.shopId = shopId;
        this.csNick = csNick;
        this.optType = optType;
        this.userLevel = userLevel;
    }
}