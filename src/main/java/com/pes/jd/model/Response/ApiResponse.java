/**  
 * Project Name:customerservicePES  
 * File Name:JxResponse.java  
 * Package Name:com.customerservicePES.model.response  
 * Date:2018年7月19日上午9:51:23  
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.  
 *  
*/  
  
package com.pes.jd.model.Response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.pes.jd.model.Enum.ApiCodeEnum;

import java.util.Collections;
import java.util.Map;
import java.util.StringJoiner;

/**  
 * ClassName:ApiResponse <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年7月19日 上午9:51:23 <br/>  
 * <AUTHOR>
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8 
 * @see        
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiResponse {

	public static final String RESULT_SINGLE_NAME = "result";
	public static final String RESULT_COLLECTION_NAME = "collectData";
	public static final String RESULT_AVG_NAME = "converge";

	public static final Map<String,Object> EMPTY_RESULT = ImmutableMap.
			<String,Object>builder().put(RESULT_SINGLE_NAME, Collections.EMPTY_LIST).build();

	private Boolean success;
	/**
	 * 接口调用状态码
	 */
	private String rpCode;
	/**
	 * 接口调用状态码
	 */
	private String rpMsg;
	/**
	 * 接口返回值
	 */
	private Map<String, Object> data;

	private String token;

	public ApiResponse() {
	}

	private ApiResponse(Builder builder) {
		setRpCode(builder.rpCode);
		setRpMsg(builder.rpMsg);
		setData(builder.data);
		setToken(builder.token);
		setSuccess(builder.success);
	}


	public static ApiResponse of(ApiCodeEnum apiCodeEnum, Map<String,Object> data){
		return of(apiCodeEnum.getCode(),apiCodeEnum.getMsg(),data);
	}


	public static ApiResponse of(String rpCode, String rpMsg, Map<String,Object> data){
		return new ApiResponse.Builder().rpCode(rpCode).rpMsg(rpMsg).data(data).build();
	}

	public static ApiResponse of(String rpCode, String rpMsg){
		return of(rpCode, rpMsg,null);
	}

	public static ApiResponse of(String rpCode, Integer rpMsg){
		return of(rpCode, rpMsg+"",null);
	}
	public static ApiResponse of(ApiCodeEnum apiCodeEnum, String msg){
		return of(apiCodeEnum.getCode(),String.format(apiCodeEnum.getMsg(), msg));
	}
	public static ApiResponse ofSuccess(){
		return of(ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
	}
	public static ApiResponse ofSuccess(String key, Object value){
		Map<String, Object> data = Maps.newHashMap();
		data.put(key, value);
		return of(ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg(), data,true);
	}

	public static ApiResponse of(ApiCodeEnum apiCodeEnum){
		return of(apiCodeEnum.getCode(),apiCodeEnum.getMsg());
	}

	public static ApiResponse of(ApiCodeEnum apiCodeEnum, Object message){
		return of(apiCodeEnum.getCode(),apiCodeEnum.getMsg(),ImmutableMap.<String,Object>builder()
				.put(ApiResponse.RESULT_SINGLE_NAME,message)
				.build());
	}


	public static ApiResponse of(String rpCode, String rpMsg, Map<String,Object> data, Boolean success){
		return new Builder().rpCode(rpCode).rpMsg(rpMsg).data(data).success(success).build();
	}

	public static ApiResponse ofSuccess(Object value){
		Map<String, Object> data = Maps.newHashMap();
		data.put(RESULT_SINGLE_NAME, value);
		return of(ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg(), data,true);
	}

	public static ApiResponse ofFail(ApiCodeEnum apiCodeEnum){
		return of(apiCodeEnum.getCode(), apiCodeEnum.getMsg(),null,false);
	}

	public static ApiResponse ofFail(ApiCodeEnum apiCodeEnum, String msg){
		return of(apiCodeEnum.getCode(),apiCodeEnum.getMsg(),ImmutableMap.<String,Object>builder()
				.put(ApiResponse.RESULT_SINGLE_NAME,msg)
				.build(),false);
	}

	public static Builder newBuilder() {
		return new Builder();
	}

	public static Builder newBuilder(ApiResponse copy) {
		Builder builder = new Builder();
		builder.rpCode = copy.getRpCode();
		builder.rpMsg = copy.getRpMsg();
		builder.data = copy.getData();
		return builder;
	}


	public String getRpCode() {
		return rpCode;
	}
	public void setRpCode(String rpCode) {
		this.rpCode = rpCode;
	}
	public String getRpMsg() {
		return rpMsg;
	}
	public void setRpMsg(String rpMsg) {
		this.rpMsg = rpMsg;
	}
	public Map<String, Object> getData() {
		return data;
	}
	public void setData(Map<String, Object> data) {
		this.data = data;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public static final class Builder {
		private String rpCode;
		private String rpMsg;
		private Map<String, Object> data;
		private String token;
		private Boolean success;

		private Builder() {
		}

		public Builder rpCode(String rpCode) {
			this.rpCode = rpCode;
			return this;
		}

		public Builder rpMsg(String rpMsg) {
			this.rpMsg = rpMsg;
			return this;
		}

		public Builder data(Map<String, Object> data) {
			this.data = data;
			return this;
		}

		public Builder data(String token) {
			this.token = token;
			return this;
		}

		public Builder success(Boolean success) {
			this.success = success;
			return this;
		}
		public ApiResponse build() {
			return new ApiResponse(this);
		}

	}

	@Override
	public String toString() {
		return new StringJoiner(", ", ApiResponse.class.getSimpleName() + "[", "]")
				.add("rpCode='" + rpCode + "'")
				.add("rpMsg='" + rpMsg + "'")
				.add("data=" + data)
				.add("token='" + token + "'")
				.toString();
	}
}
  
