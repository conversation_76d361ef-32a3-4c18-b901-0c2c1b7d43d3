package com.pes.jd.controller;

import com.pes.jd.business.DashboardBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.DeptInfoParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.ImportExcelUtil;
import com.pes.jd.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/6/3 13:43
 */
@Controller
@RequestMapping("/dashboard/")
public class DashboardController {
    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);



    @Autowired
    private DashboardBusiness dashboardBusiness;

    /**
     * 登陆接口
     * @param request
     * @param username
     * @param password
     * @return
     */
    @RequestMapping(value = "login")
    @ResponseBody
    public ApiResponse login(HttpServletRequest request, @RequestParam(name = "username", required = true) String username,
                             @RequestParam(name = "password", required = true) String password) {
        ApiResponse apiResponse = null;
        try {
            apiResponse = dashboardBusiness.dashboardLogin(username, password, request);
        } catch (Exception e) {
            logger.error("看板登录error : ", e.getMessage());
            apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_14);
        }
        return apiResponse;
    }

    /**
     * 测试接口
     * @param request
     * @return
     */
    @RequestMapping(value = "initUploadExcelPage")
    public String initUploadExcelPage(HttpServletRequest request){
        try {
            System.out.println("hannnnnnnnnnnnnn");
        } catch (Exception e) {
            logger.error("sysLoginEntrance : error", e.getMessage());
        }
        return "uploadExcel";
    }

    /**
     * 上传Excel文件信息 包括部门对于关系，用户登陆信息
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("uploadExcelInfo")
    @ResponseBody
    public ApiResponse form(HttpServletRequest request,@RequestParam(name = "filerId", required = true) String filerId)throws Exception{
        if(org.apache.commons.lang3.StringUtils.isBlank(filerId)){
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CBR_01_03);
        }
        MultipartHttpServletRequest multipartRequest = null;
        try {
            multipartRequest = (MultipartHttpServletRequest) request;
            InputStream in =null;
            List<List<Object>> listob = null;
            MultipartFile file = multipartRequest.getFile("upfile");

            if(file.isEmpty()){
                throw new Exception("文件不存在！");
            }
            in = file.getInputStream();
            listob = new ImportExcelUtil().getBankListByExcel(in,file.getOriginalFilename());
            in.close();
            List<DeptInfoParam> deptInfos = new ArrayList<>();
            //该处可调用service相应方法进行数据保存到数据库中，现只对数据输出
            for (int i = 0; i < listob.size(); i++) {
                List<Object> lo = listob.get(i);
                if(lo == null){
                    break;
                }
                if("".equals(lo.get(0))){
                    break;
                }
                DeptInfoParam deptInfoParam = new DeptInfoParam();
                if("0".equals(lo.get(0))){
                    logger.info("事业群名称为空！"+i);
                    continue;
                }
                deptInfoParam.setDeptName00(String.valueOf(lo.get(0)));
                if(CommonConstants.FILER_VALUE.equals(lo.get(1))){
                    deptInfoParam.setType(CommonConstants.TYPE_0);
                    deptInfoParam.setUsername(String.valueOf(lo.get(6)));
                    deptInfoParam.setPassword(String.valueOf(lo.get(7)));
                    deptInfos.add(deptInfoParam);
                    continue;
                } else {
                    deptInfoParam.setDeptName01(String.valueOf(lo.get(1)));
                }

                if(CommonConstants.FILER_VALUE.equals(lo.get(2))){
                    deptInfoParam.setType(CommonConstants.TYPE_1);
                    deptInfoParam.setUsername(String.valueOf(lo.get(6)));
                    deptInfoParam.setPassword(String.valueOf(lo.get(7)));
                    deptInfos.add(deptInfoParam);
                    continue;
                } else {
                    deptInfoParam.setDeptName02(String.valueOf(lo.get(2)));
                }

                if(CommonConstants.FILER_VALUE.equals(lo.get(3))){
                    deptInfoParam.setType(CommonConstants.TYPE_2);
                    deptInfoParam.setUsername(String.valueOf(lo.get(6)));
                    deptInfoParam.setPassword(String.valueOf(lo.get(7)));
                    deptInfos.add(deptInfoParam);
                    continue;
                } else {
                    deptInfoParam.setDeptName03(String.valueOf(lo.get(3)));
                }

                if(CommonConstants.FILER_VALUE.equals(lo.get(4))){
                    deptInfoParam.setType(CommonConstants.TYPE_3);
                    deptInfoParam.setUsername(String.valueOf(lo.get(6)));
                    deptInfoParam.setPassword(String.valueOf(lo.get(7)));
                    deptInfos.add(deptInfoParam);
                    continue;
                } else {
                    deptInfoParam.setErpId(String.valueOf(lo.get(4)));
                    deptInfoParam.setErpName(String.valueOf(lo.get(5)));
                }
                deptInfoParam.setType(CommonConstants.TYPE_4);
                deptInfoParam.setUsername(String.valueOf(lo.get(6)));
                deptInfoParam.setPassword(String.valueOf(lo.get(7)));
                deptInfos.add(deptInfoParam);
            }
            Collections.sort(deptInfos, new Comparator<DeptInfoParam>() {
                public int compare(DeptInfoParam arg0, DeptInfoParam arg1) {
                    int type0 = Integer.valueOf(arg0.getType());
                    int type1 = Integer.valueOf(arg1.getType());
                    if (type1 < type0) {
                        return 1;
                    } else if (type0 == type1) {
                        return 0;
                    } else {
                        return -1;
                    }
                }
            });
            dashboardBusiness.uploadExcelInfo(deptInfos,filerId);
        } catch (Exception e) {
            logger.error("上传Excel文件异常",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_BR_01_03);
        }

        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
    }

    /**
     * 获取对应部门关系信息接口
     * @param request
     * @param id 用户登陆id
     * @param deptId 部门id
     * @param type 类型（0：管理员，1：一级部门，2：二级部门，3：三级部门，4：四级部门）
     * @return
     */
    @RequestMapping(value = "getDeptRelation")
    @ResponseBody
    public ApiResponse getDeptRelation(HttpServletRequest request, @RequestParam(name = "id", required = true) String id,
                             @RequestParam(name = "deptId", required = true) String deptId,@RequestParam(name = "type", required = true) String type) {
        ApiResponse apiResponse ;
        try {
            if(!validateGetDeptRelation(id,deptId,type)){
                apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_02);
                return apiResponse;
            }
            apiResponse = dashboardBusiness.getDeptRelation(id, deptId, type);
        } catch (Exception e) {
            logger.error("看板登录error : ", e.getMessage());
            apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_SY_02_01);
        }
        return apiResponse;
    }

    public boolean validateGetDeptRelation(String id ,String deptId,String type){
        if(org.apache.commons.lang3.StringUtils.isBlank(id)){
            logger.info("id is null");
            return false;
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(deptId)){
            logger.info("deptId is null");
            return false;
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(type)){
            logger.info("type is null");
            return false;
        }
        return true;
    }

    /**
     * 获取上传Excel文件信息
     * @param request
     * @return
     */
    @RequestMapping("getUploadExcelInfo")
    @ResponseBody
    public ApiResponse getUploadExcelInfo(HttpServletRequest request) {
        ApiResponse apiResponse;
        try {
            apiResponse = dashboardBusiness.getUploadExcelInfo();
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_02_01);

        }
        return apiResponse;
    }

    /**
     * 新增文件名接口
     * @param request
     * @return
     */
    @RequestMapping("uploadFilerInfo")
    @ResponseBody
    public ApiResponse uploadFilerInfo(HttpServletRequest request,@RequestParam(name = "filerName", required = true) String filerName) {
        if(org.apache.commons.lang3.StringUtils.isBlank(filerName)){
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_KB_00_00);
        }
        ApiResponse apiResponse;
        try {
            apiResponse = dashboardBusiness.uploadFilerInfo(filerName);
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_02_01);

        }
        return apiResponse;
    }


}
