package com.pes.jd.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.request.tool.JosIsvTokenEncryptionRequest;
import com.jd.open.api.sdk.response.tool.JosIsvTokenEncryptionResponse;
import com.pes.jd.business.*;
import com.pes.jd.business.opt.MarketingActivityOptV3;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.constants.AppConstants;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.data.api.UserInfoOperator;
import com.pes.jd.data.api.VenderShopOperator;
import com.pes.jd.data.convert.ShopInfoDateConverter;
import com.pes.jd.exception.ApiInvokException;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.jwt.JwtConstants;
import com.pes.jd.jwt.KeyUtils;
import com.pes.jd.model.AO.AuthorizationUserInfo;
import com.pes.jd.model.AO.JDSessionKey;
import com.pes.jd.model.BO.LoginResultBO;
import com.pes.jd.model.DO.ShopInfoDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.LoginLogUploadParam;
import com.pes.jd.model.Param.LoginUserParam;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.MarketingActivityV2VO;
import com.pes.jd.model.VO.MarketingActivityVO;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.ms.model.Enum.PopUpsTypeEnum;
import com.pes.jd.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Controller
@RequestMapping("/login")
@RefreshScope
public class LoginController extends BaseController{

	@Autowired
	private LoginBussiness loginBussiness;
	private Logger logger = LoggerFactory.getLogger(LoginController.class);

	@Autowired
	private ShopInfoDateConverter shopInfoDateConverter;

	@Autowired
	private ShopSysSettingBusiness shopSysSettingBusiness;
	@Autowired
	private UserInfoOperator userInfoOperator;

	@Autowired
	private ShopSysManagerBusiness shopSysManagerBusiness;

	@Autowired
	private LoginInterfaceBusiness loginInterfaceBusiness;

	@Autowired
	private ShopAccountBussiness shopAccountBussiness;

	@Autowired
	private OnlineNoticeBusiness onlineNoticeBusiness;

	@Autowired
	private MarketingActivityBusiness marketingActivityBusiness;

	@Autowired
	private SgBussiness sgBussiness;

	@Autowired
	private MemberCentreBusiness memberCentreBusiness;
	@Autowired
	private MarketingActivityOptV3 marketingActivityOptV3;

	private final String loginErrorPath = "error/error";
    private final String loginErrorPopTipsPath = "error/match-pop";
    private final String loginErrorSelfTipsPath = "error/match-self";
	private final String loginInitGuidePath = "error/init-guide-error";
	private final String loginSelfTipsPath = "error/self-tips-error";

	@Value("${web.target.url}")
	private String webTargetUrl;


	@Value("${web.targetTool.url}")
	private String webTargetToolUrl;

	@Value("${web.course.url}")
	private String webCourseUrl;
	@Resource
	private RedisOperator<String,Object> redisOperator;

	@RequestMapping("/loginOffLine")
	public String loginOffLine(){
		String token=getRequestCookie();
		redisOperator.delete(token);
		return "welcome";
	}

	@Value("${web.target.self.url}")
	private String webTargetSelfUrl;

	@Value("${web.targetTool.self.url}")
	private String webTargetToolSelfUrl;

	@Value("${web.targetMobile.url}")
	private String webTargetMoblieUrl;

	@Value("${web.targetMobile.self.url}")
	private String webTargetMoblieSelfUrl;

	@Resource
	private VenderShopOperator venderShopOperator;

	private static final String REJECT_KEY = "web-reject-code-";
	private static final String USER_NICK = "web-user-nick-";
	private static final String SHOP_TYPE = "web-shop-type-";

//	private static final LoadingCache<String, Boolean> USER_INTERCEPTOR = CacheBuilder.newBuilder()
//			.maximumSize(10000)
//			.expireAfterWrite(10, TimeUnit.MINUTES)
//			.build(
//					new CacheLoader<String, Boolean>() {
//						@Override
//						@ParametersAreNonnullByDefault
//						public Boolean load(String key){
//							return Boolean.FALSE;
//						}
//					});

	/**
	 * 客户登陆：pop/自营
	 * @param request
	 * @param state
     * @param code
	 * @param error
	 * @param error_description
	 * @return
	 */
	@RequestMapping(value="/main")
	public ModelAndView login(HttpServletRequest request,String state, String code, String error, String error_description) {
		//防刷
		try {
			if(StringUtils.isNotBlank(redisOperator.get(REJECT_KEY + code))){
				logger.info("==========>拦截用户code:{}", code);
				Map<String, Object> retMap = new HashMap<>();
				//返回前端给默认值
				retMap.put("nick", "");
				retMap.put("openId","");
				retMap.put("shopId", 0L);
				retMap.put("msg", "授权失败");
				retMap.put("errMsg", "授权失败,请重新登录");
				return new ModelAndView(loginErrorPath, "result", retMap);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		long s1,s2;
		s1=System.currentTimeMillis();
		String userAgent=request.getHeader("User-Agent");
		logger.info("--------------------------login 进入登录方法---userAgent:{} *** state：{}***code :{}******error：{}------------------------",userAgent,state,code,error);
		Map<String, Object> retMap = new HashMap<>();
		//返回前端给默认值
		String pin="";
		String openId="";
		Long shopId=0L;
		retMap.put("nick", pin);
		retMap.put("openId",openId);
		retMap.put("shopId",shopId);
		//授权处理
		if (error != null) {
			logger.info("网页登录失败：[code:{}, error:{}]", code, error_description);
			retMap.put("msg", "登录失败");
			if ("access_denied".equals(error)) {
				retMap.put("errCode", "login_permission_no");
				retMap.put("errMsg", "没有应用权限，请向店铺【主账号】申请【应用权限】");
			} else {
				retMap.put("errCode", error);
				retMap.put("errMsg", error_description);
			}
			return new ModelAndView(loginErrorPath, "result", retMap);
		}
		//获取sessionKey
		JDSessionKey authParam;
		try {
			authParam = ApiClientUtil.getSessionKeyByCode(code);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			retMap.put("msg", "授权失败");
			retMap.put("errMsg", "授权失败,请重新登录");
			return new ModelAndView(loginErrorPath, "result", retMap);
		}

		if(authParam==null || authParam.getCode() == 304){
//			USER_INTERCEPTOR.put(code, Boolean.TRUE);
			redisOperator.set(REJECT_KEY + code, "1");
			redisOperator.expire(REJECT_KEY + code, 10, TimeUnit.MINUTES);
			retMap.put("msg", "授权失败");
			retMap.put("errMsg", "授权失败,请重新登录");
			return new ModelAndView(loginErrorPath, "result", retMap);
		}

		 openId = authParam.getOpen_id();
		// 淘宝网页端的session_key
		String sessionKey = authParam.getAccess_token();
		// 登录的账号ID
		String uid = authParam.getUid();

		Integer authCode=authParam.getCode();
		AuthorizationUserInfo user=null;
		// "code":405,"msg":"您好[jd_qipilangsgzy],应用[321CEAB001F59FDDA67DC388C0575958]已发布到服务市场，请到服务市场订购"
		if(authParam.getCode()!=null&&authParam.getCode()==405){
			String url="<a  target='_blank' href=\""+webCourseUrl+"\">点此查看教程</a>";
			retMap.put("msg", "登录失败！");
			retMap.put("errMsg", "您的账号无权限使用魔方应用，请联系主账号授权，"+url);
			return new ModelAndView(loginErrorPath, "result", retMap);
		}
		//获取pin信息
		try {
			logger.info("转换前state:{}",state);
			user=decodeStateOfPin(state);
			if(StringUtils.isNotBlank(user.getUser_name())){
				logger.info("get  pin:{}",user.getUser_name());
				pin=user.getUser_name().toLowerCase();
			}

		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			retMap.put("errCode", "state analysis error");
			retMap.put("errMsg", "获取用户昵称失败");
			return new ModelAndView(loginErrorPath, "result", retMap);
		}


		// 获取店铺信息
		ShopInfoDO shopInfoDO = null;
		try {
			long t1 = System.currentTimeMillis();
			shopInfoDO = shopInfoDateConverter.getShopInfo(pin, sessionKey, openId);
			logger.info("VenderInfoQueryByPinRequest pin :{}, time :{}ms", pin, System.currentTimeMillis()-t1);
		} catch (GainShopDataFailException e) {
			logger.error("getShopInfo error:{}",e.getErrorMsg());
			if(StringUtils.equals(e.getErrorCode(), "19")){
				retMap.put("msg", "登录失败！");
				retMap.put("errMsg", "该店铺订购已过期");
			}else {
				retMap.put("msg", "登录失败！");
				retMap.put("errMsg", "获取店铺信息失败！");
			}
			return new ModelAndView(loginErrorPath, "result", retMap);
		}catch (Exception e) {
			retMap.put("msg", "登录失败！");
			retMap.put("errMsg", "获取店铺信息失败！");
			return new ModelAndView(loginErrorPath, "result", retMap);
		}

		if (shopInfoDO==null) {
			retMap.put("msg", "登录失败！");
			retMap.put("errMsg", "获取店铺信息为空!");
			return new ModelAndView(loginErrorPath, "result", retMap);
		}
		shopId=shopInfoDO.getShopId();

		String userNick="" ;
		//获取展示昵称信息
		try {
			long t3 = System.currentTimeMillis();
			String userNickKey = USER_NICK + "/" + pin + "/" + openId;
			userNick = redisOperator.get(userNickKey);
			if(StringUtils.isBlank(userNick)){
				userNick = userInfoOperator.getUserInfoBySessionKeyByUid(uid, sessionKey);
				redisOperator.set(userNickKey, userNick);
				redisOperator.expire(userNickKey, 6 * 60, TimeUnit.MINUTES);
			}else {
				logger.info("======>get userNick from cache, uid:{}, userNick:{}", uid, userNick);
			}
			logger.info("****userNick****:"+userNick+"NicknamebyuidQueryRequest time"+(System.currentTimeMillis()-t3)+"ms");
		} catch (GainShopDataFailException e) {
			logger.error("getUserInfoBySessionKeyByUid error:{}",e.getErrorMsg());
			if(StringUtils.equals(e.getErrorCode(), "19")){
				retMap.put("msg", "登录失败！");
				retMap.put("errMsg", "该店铺订购已过期");
			}else{
				retMap.put("msg", "登录失败！");
				retMap.put("errMsg", "获取用户展示昵称失败");
			}
			return new ModelAndView(loginErrorPath, "result", retMap);
		} catch (Exception e) {
			retMap.put("msg", "登录失败！");
			retMap.put("errMsg", "拉取用户昵称信息失败！");
			return new ModelAndView(loginErrorPath, "result", retMap);
		}

		if (StringUtils.isBlank(userNick)||StringUtils.isBlank(pin)) {
			retMap.put("msg", "登录失败！");
			retMap.put("errMsg", "获取用户信息为空!");
			return new ModelAndView(loginErrorPath, "result", retMap);
		}

		//手机端
		if(MobileDeviceUtils.isMobileDevice(request)){
			try {
				logger.info("pin:{} pop手机登陆开始！",pin);
				LoginResultBO result=loginBussiness.mobileLogin(shopId,pin);
				logger.info("pin:{} pop手机登陆结束！result：{}",pin,result.toString());
				if(result.getRetCode()==0){
					result.setToken(KeyUtils.genUniqueKey());
					String moblieToolUrl=webTargetMoblieUrl+"?shopId="+shopId+"&showNick="+URLEncoder.encode(userNick,"UTF-8");
					logger.info("pin:{} pop手机登陆成功！ moblieToolUrl:{} 耗时：{}ms",pin, moblieToolUrl,(s2=System.currentTimeMillis())-s1);
					return new ModelAndView("redirect:" + moblieToolUrl,"token",result.getToken());
				}else if (result.getRetCode() == 3) {
					//初始化引导页面
					return new ModelAndView(loginInitGuidePath, "", "");
				} else if (result.getRetCode() == 4) {
					//自营提示
					return new ModelAndView(loginSelfTipsPath, "", "");
				} else {
					setResultMap("登录失败！", retMap, result);
					return new ModelAndView(loginErrorPath, "result", retMap);
				}
			} catch (Exception e) {
				logger.error(" pin :{}mobileLogin error:{}",pin,e.getMessage(), e);
				retMap.put("msg", "登录失败！");
				retMap.put("errMsg", "登录失败");
				return new ModelAndView(loginErrorPath, "result", retMap);
			}
		}

		LoginUserParam userParam=new LoginUserParam();

		try {
			long t2 = System.currentTimeMillis();
			ShopInfoDO shopType;
			String shopTypeKey = SHOP_TYPE + "/" + openId;
			String shopInfoStr = redisOperator.get(shopTypeKey);
			if(StringUtils.isBlank(shopInfoStr)){
				shopType = shopInfoDateConverter.getShopType(sessionKey);
				redisOperator.set(shopTypeKey, JSONObject.toJSONString(shopType));
				redisOperator.expire(shopTypeKey, 24, TimeUnit.HOURS);
			}else{
				shopType = JSONObject.parseObject(shopInfoStr, ShopInfoDO.class);
				logger.info("======>get shopType from cache, shopId:{}", shopType.getShopId());
			}
			logger.info("VenderVbinfoGetBasicVenderInfoByVenderIdRequest pin :{}, time :{}",pin,System.currentTimeMillis()-t2);
			if(null != shopType){
				userParam.setVenderType(shopType.getVenderType());
			}
		} catch (ApiInvokException e) {
			e.printStackTrace();
		}

		logger.info("get authCode:{} openId:{},sessionKey:{},***refreskSessionKey:{},pin:{},uid:{},expires_in:{},shopId:{},shopName:{},-----userAgent:{}",authCode,openId,sessionKey,authParam.getRefresh_token(),pin,uid,authParam.getExpires_in(),shopInfoDO.getShopId(),shopInfoDO.getShopName(),userAgent);

		retMap.put("nick", pin);
		retMap.put("openId",openId);
		retMap.put("shopId",shopId);
		//拿到访问主机Ip
		String ipAddress;
		try{
			ipAddress = getIpAddress(request);
			userParam.setIp(ipAddress);
			System.out.println("访问主机Ip:"+ipAddress);
		}catch (IOException ioe){
			logger.error(ioe.getMessage(),ioe);
		}

		userParam.setUid(uid);
		userParam.setUserNick(userNick);
		userParam.setPin(pin);
		userParam.setSessionKey(sessionKey);
		userParam.setEndDate(new Date(user.getEnd_date()));
		userParam.setItemCode(user.getItem_code());
		userParam.setOpenId(openId);
		userParam.setRefreshSessionKey(authParam.getRefresh_token());
		//校验pop和self 订购类型是否对应
		if(CommonConstants.SELF_TYPE.equals(userParam.getVenderType()) && !CommonConstants.SELF_TYPE_CODE.equals(userParam.getItemCode())){
            return new ModelAndView(loginErrorPopTipsPath, "", "");
		}
		if(CommonConstants.POP_TYPE.equals(userParam.getVenderType()) && CommonConstants.SELF_TYPE_CODE.equals(userParam.getItemCode())){
            return new ModelAndView(loginErrorSelfTipsPath, "", "");
        }

			logger.info("--get  ----pin:{},shopId:{},shopTitle:{}",pin,shopInfoDO.getShopId(),shopInfoDO.getShopName());
			try {
				Integer flag= CommonConstants.webFlag;
				if(MobileDeviceUtils.isPcDevice(request)){
					flag= CommonConstants.pluginFlag;
				}else{
					if(MobileDeviceUtils.isMobileDevice(request)){
						flag= CommonConstants.mobileFlag;
					}
				}
				logger.info("--------------------------pin:{} 进入commonLogin方法---------------------------",pin);
				LoginResultBO result = commonLogin(userParam, CommonConstants.CLENT_WEB, shopInfoDO,flag);
				logger.info("--------------------------pin:{} commonLogin方法结束---------------------------",pin);
				logger.info("pin:{} result.code:{}:errMsg{},sessionMap:{}",pin, result.getRetCode(),result.getErrMsg(),result.getSessionMap());
				if (result.getRetCode() == 0) {
					//咚咚和京麦插件
					if(MobileDeviceUtils.isPcDevice(request)){
						logger.info("{}: - 插件登录成功!",pin);
						String csNick=URLEncoder.encode(pin.toLowerCase(),"UTF-8");
						String pluginToolUrl=webTargetToolUrl+"?csNick="+csNick+"&shopId="+shopInfoDO.getShopId()+"&openId="+openId;
						String pluginToolSelfUrl=webTargetToolSelfUrl+"?csNick="+csNick+"&shopId="+shopInfoDO.getShopId();
						String token = setLoginInfoSession(openId,result.getSessionMap());
						if (1 == userParam.getVenderType()){
							if(StringUtils.isNotBlank(token)){
								pluginToolSelfUrl += "&token=" + token;
							}
							logger.info(pin + " - 自营插件登录成功! ---pluginToolSelfUrl:{}",pluginToolSelfUrl);
							return new ModelAndView("redirect:" + pluginToolSelfUrl,"result", retMap);
						}else {
							if(StringUtils.isNotBlank(token)){
								pluginToolUrl += "&token=" + token;
							}
							logger.info(pin + " - pop插件登录成功! ---pluginToolUrl:{}",pluginToolUrl);
							return new ModelAndView("redirect:" + pluginToolUrl,"result", retMap);
						}
					}else{
						try {
							String token = setLoginInfoSession(openId,result.getSessionMap());
							result.setToken(token);
						} catch (Exception e) {
							logger.error("set setLoginInfoSession error:{}",e.getMessage(),e);
						}
						//手机端
//						if(MobileDeviceUtils.isMobileDevice(request)){
//							logger.info("{}: - 手机登录成功!,token:{}",pin,result.getToken());
//							if (1 == userParam.getVenderType()){
//								logger.info("pin:{}自营手机登陆成功！",pin);
//								return new ModelAndView("redirect:" + webTargetMoblieSelfUrl,"token",result.getToken());
//							} else {
//								logger.info("pin:{} pop手机登陆成功！",pin);
//								return new ModelAndView("redirect:" + webTargetMoblieUrl,"token",result.getToken());
//							}
//						}else{
							//网页端
							logger.info("{}: - 网页登录成功!,token:{}",pin,result.getToken());
							if (1 == userParam.getVenderType()){
								logger.info("pin：{}自营登陆成功！",pin);
								return new ModelAndView("redirect:" + webTargetSelfUrl,"token",result.getToken());
							} else {
								logger.info("pin：{} pop登陆成功！",pin);
								return new ModelAndView("redirect:" + webTargetUrl,"token",result.getToken());
							}
						//}

					}

				} else if (result.getRetCode() == 2) {
					// 新用户【首次登录】，请使用【主账号】
					setResultMap("登录失败！", retMap, result);
					return new ModelAndView(loginErrorPath, "result", retMap);
				} else {
					setResultMap("登录失败！", retMap, result);
					return new ModelAndView(loginErrorPath, "result", retMap);
				}

			} catch (Exception e) {
				logger.error(" pin :{}login error:{}",pin,e.getMessage(), e);
				retMap.put("msg", "登录失败！");
				retMap.put("errMsg", "登录失败");
				return new ModelAndView(loginErrorPath, "result", retMap);
			}



	}

	/**
	 * 模拟账号登录
	 * @return
	 */
	private JDSessionKey testUser() {
		JDSessionKey authParam = new JDSessionKey();
		authParam.setOpen_id("OgbUe-EFz0UaElu8OXOZvM4iGwMDTOuKzBpll--sd6s");
		authParam.setAccess_token("65770056292843a6b255e149c3788b6dnzaw");
		authParam.setUid("5808225487");
		authParam.setCode(400);
		authParam.setRefresh_token("74e5a00776694674bf86f0ded63545eblnwe");
		return authParam;
	}

	private void setResultMap(String msg, Map<String, Object> retMap, LoginResultBO result) {
		retMap.put("msg", msg);
		retMap.put("errMsg", result.getErrMsg());
		retMap.put("reCode", result.getRetCode());

	}

	/**
	 * @Description:（多店铺切换店铺）
	*
	 */
	@RequestMapping(value="/switchShop",method=RequestMethod.POST)
	@ResponseBody
	public ApiResponse switchShop(HttpServletRequest request, HttpServletResponse response, String shopId) throws LoginAuthException {
        String headerAuthToken = request.getHeader(JwtConstants.AUTH_TOKEN.toLowerCase());
        String sessionKeyToken=JwtConstants.TOKEN_SESSION + headerAuthToken;
        if (StringUtils.isBlank(shopId)) {
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_08);
        }
        ShopDTO mainShops = this.getMainShop();
        if (mainShops == null) {
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_09);
        }
        ShopUserDTO mainUser=this.getMainUser();
        try {
            ShopDTO selectedShop;
            if (mainShops.getShopId().equals(Long.valueOf(shopId))) {
                selectedShop = mainShops;
            } else {
                selectedShop = getMainShopMemberShop(mainShops.getShopId(), Long.valueOf(shopId));
            }
            if (selectedShop == null) {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_09);
            }
            if(StringUtils.equals(selectedShop.getStatus(), "expired")){
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_07);
            }
            logger.info("开始-切换店铺：[" + selectedShop.getTitle() + "]");

			try {
				venderShopOperator.checkShopExpireBySessionKey(selectedShop.getSessionKey());
			} catch (GainShopDataFailException e) {
				if(e.getErrorCode().equals("19")) {
					if (org.apache.commons.lang3.StringUtils.isNotBlank(selectedShop.getOptionSessionKey())) {
						selectedShop.setSessionKey(selectedShop.getOptionSessionKey());
					} logger.info("shopName {} main SessionKey is expire", selectedShop.getTitle());
				} else {
					logger.info("shopName {} sub SessionKey is empty no update sessionKey", selectedShop.getTitle());
				}

			}catch (Exception e) {
				logger.error("shopName:{} checkShopExpireBySessionKey error:{}",selectedShop.getTitle(),e.getMessage(),e);
			}

			// 获取店铺信息
			ShopInfoDO shopInfo = new ShopInfoDO();
			String tokenKey=getRequestCookie();
			Integer authVistorFlag = (Integer) redisOperator.getHashKey(tokenKey,"authVistorFlag");
			shopInfo.setShopId(selectedShop.getShopId());
			shopInfo.setShopName(selectedShop.getTitle());
			shopInfo.setVenderId(selectedShop.getVenderId());
			shopInfo.setColType(selectedShop.getColType());
			LoginUserParam userParam=new LoginUserParam();
			userParam.setUid(selectedShop.getUserId()+"");
			userParam.setPin(selectedShop.getSellerNick());
			userParam.setSessionKey(selectedShop.getSessionKey());
			ShopUserDTO user=getUserInfoByShopIdByUserNick(selectedShop.getShopId(),selectedShop.getSellerNick());
			if(user==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_10);
			}
			userParam.setUserNick(user.getShowNick());
			userParam.setEndDate(user.getSubscribeDeadLine());
			userParam.setItemCode(user.getItemCode());
			userParam.setOpenId(user.getOpenId());
			userParam.setRefreshSessionKey(user.getRefreshSessionKey());
			LoginResultBO result = commonLogin(userParam, CommonConstants.CLENT_SWITCH, shopInfo, CommonConstants.webFlag);

			if (result.getRetCode() == 0) {
			Map<String, Object>	sessionMap= result.getSessionMap();
				if(sessionMap != null){
//					if(!authVistorFlag.equals(2)){
					if(!"2".equals(authVistorFlag)){
						sessionMap.put("authVistorFlag",mainUser.getMainAccount() && mainUser.getVistorCheck()? 1: 2);//是否显示访问密码 1：显示 2：不显示
					}
					sessionMap.put("switchShop",true);
					redisOperator.putAll(sessionKeyToken, sessionMap);
					redisOperator.expire(sessionKeyToken,12*60, TimeUnit.MINUTES);
				}
				logger.info(selectedShop.getTitle() + " 切换店铺成功! token:"+sessionKeyToken);

				return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			}else if (result.getRetCode() == 2) {
				// 新用户【首次登录】，请使用【主账号】
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_05.getCode(),result.getErrMsg());
			} else {
				//其余报错信息
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_06.getCode(),result.getErrMsg());
			}
		} catch (Exception e) {
			logger.error("switch shop error:{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_04);
		}

	}
	private ShopUserDTO getUserInfoByShopIdByUserNick(Long shopId,String userNick) throws Exception{
		ApiResponse apiResponse= loginBussiness.getUserInfoByShopIdByUserNick(shopId,userNick);
		ShopUserDTO user=null;
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			Object obj=	apiResponse.getData().get("user");
			if(obj!=null){
				user=JSON.parseObject(JSON.toJSONString(obj), ShopUserDTO.class);
			}
		}else{
			logger.error("web get user error:{}",apiResponse.getRpMsg());
		}
		return user;
	}
	@RequestMapping(value="/getAppCommonInfo",method=RequestMethod.POST)
	@ResponseBody
	public ApiResponse getAppCommonInfo(HttpServletRequest request) throws LoginAuthException {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> result = new HashMap<>();
		String tokenKey = getRequestCookie();
		ShopUserDTO currentUser = this.getCurrentUser();
		//移动端只要当前登录对象
			ShopUserDTO mainUser = this.getMainUser();
			ShopDTO mainShop = this.getMainShop();
			ShopSystemsettingDTO system=this.getShopSystemsetting();
			ShopDTO shop = this.getCurrentShop();
			Boolean oldShop = this.isOldShop();
			try {
				//*********多店铺切换当前用户会变主店铺需要做判断*******//
				if (oldShop) {
					//查询登录账号的角色
					if (this.isFirstLoginShop()) {
						shopAccountBussiness.getUserRolePutRedis(currentUser, tokenKey);
					}
					logger.info("店铺切换为登录用户");
					currentUser = this.getMainUser();
				}

				Boolean aBoolean = initializationFlag(shop.getShopId());
				ShopUserDTO seller = JSON.parseObject(JSON.toJSONString(redisOperator.getHashKey(tokenKey, "manager")), ShopUserDTO.class);
				String itemCode = (String)  redisOperator.getHashKey(tokenKey, "itemCode");
				String adminManageRole = (String)  redisOperator.getHashKey(tokenKey, "adminManageRole");
				Long expiredDay=Long.valueOf(redisOperator.getHashKey(tokenKey,"expiredDay")+"");
				Integer authVistorFlag =(Integer)redisOperator.getHashKey(tokenKey,"authVistorFlag") ;
				String manageRole = (String) redisOperator.getHashKey(tokenKey, "role");
				Integer sysAdminFlag = (Integer) redisOperator.getHashKey(tokenKey, "sysAdminFlag");

				result.put("mainShop", mainShop);
				result.put("shop", shop);
				result.put("authVistorFlag", authVistorFlag);
				if(logger.isDebugEnabled()){
					logger.debug("authVisitor-{}",authVistorFlag);
				}
				result.put("currentUser", currentUser);
				result.put("mainUser", mainUser);
				result.put("itemCode", itemCode);
				result.put("expiredDay", expiredDay);
				result.put("initFlag", aBoolean);
				result.put("enquiryValidDurationTime", system.getEnquiryValidDurationTime());
				result.put("outStockValidDurationTime", system.getOutStockValidDurationTime());
				result.put("manage",adminManageRole==null?manageRole:adminManageRole);
				result.put("showMultiShopSetting", Objects.equals(currentUser.getUserId(),mainUser.getUserId()));//显示多店铺设置

				//判断 用户是否 首次购买魔方，未完成初始化
				String userPin = currentUser.getNick();
				boolean firstLoginFlag = loginBussiness.getUserOperationLogByNickAndShop(shop.getShopId(), userPin, PopUpsTypeEnum.FIRST_LOGIN.getType());
				result.put("firstBuyMoFangFlag", firstLoginFlag && !aBoolean);


				//判断用户授权是否过期(当天首次登录时提醒)
				boolean todayFirstLoginFlag = loginBussiness.getUserOperationLogByNickAndShopAndTime(shop.getShopId(), new Date(), new Date(), userPin, PopUpsTypeEnum.AUTH_EXPIRED.getType());
				boolean userAuthExpiredFlag = !currentUser.getMainAccount() &&  seller.getAuthDeadLine() != null
						&& seller.getAuthDeadLine().compareTo(new Date()) <= 0 && todayFirstLoginFlag;//authDeadLine 小于当前时间即为失效
				result.put("userAuthExpiredFlag", userAuthExpiredFlag);


				//判断店铺订购后即将到期(按照订购期限的不同 系统给出弹窗)
				result.putAll(handleOrderDeadLineRemindFlag(shop.getShopId(), userPin, expiredDay));

				//用户 简洁版完整版查询
				result.put("interfaceType", loginInterfaceBusiness.getUserInterfaceType(userPin));

				//上线通知弹窗
				result.putAll(handleOnlineNoticeFlag(shop.getShopId(), userPin));

				Integer count=shopSysSettingBusiness.selectCsCountByShopIdByStatus(shop.getShopId(),1);
				Boolean isDownGradeFlag=false;
				Boolean subDownGradeFlag=false;
				//主账号并且当前设置的正常客服数大于订购版本数量
				if(sysAdminFlag!=null&&sysAdminFlag==2){
					if(currentUser.getMainAccount()&&count>shop.getSubuserNum()){
						isDownGradeFlag=true;
					}
					if(!currentUser.getMainAccount()&&count>shop.getSubuserNum()){
						subDownGradeFlag=true;
					}
				}

				result.put("downGradeFlag",isDownGradeFlag);
				result.put("subDownGradeFlag",subDownGradeFlag);
				//会员中心
//				Boolean memberFlag = loginBussiness.getUserOperationLogByNickAndShop(shop.getShopId(), userPin, PopUpsTypeEnum.MEMBER_CENTRE.getType());
				boolean memberFlag = false;
				if(aBoolean){
					memberFlag = memberCentreBusiness.judgePopUpOrNot(shop.getShopId(), userPin, shop.getTitle());
				}
				result.put("memberFlag", memberFlag && sysAdminFlag == 2);

				//判断是否有活动通知
				result.putAll(handleActivityV2(shop.getShopId(), userPin));

//			/**
//			 * 校验实名认证
//			 */
//			result.put("checkAuthentication", sgBussiness.checkAuthentication(shop, userPin));
		apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);

			}catch (LoginAuthException ee) {
				throw ee;
			} catch (Exception e) {
				apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
				logger.error("error information：" + e.getMessage(), e);
			}



		apiResponse.setData(result);
		return apiResponse;
	}


	@RequestMapping("/getShopInitDataFlag")
	@ResponseBody
	public ApiResponse getShopInitDataFlag() throws LoginAuthException {
		ApiResponse apiResponse;
		Map<String, Object> data = new HashMap<>();
		int initDataFlag = CommonConstants.SHOP_INIT_DATA_NO;
		ShopDTO currentShop = this.getCurrentShop();
		try {
			ShopDTO shop = getShopInfo(currentShop);
			if (shop != null) {
				initDataFlag = shop.getInitDataFlag();
			}
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			apiResponse= ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_03);
		}
		data.put("initShopFlag", initDataFlag);
		apiResponse.setData(data);
		return apiResponse;
	}
	private ShopDTO getShopInfo(ShopDTO shop) throws Exception{
		ApiResponse apiResponse= loginBussiness.getShopInfo(this.getCurrentShop());
		ShopDTO shopInfo=null;
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			Object obj=	apiResponse.getData().get("shop");
			if(obj!=null){
				shopInfo=JSON.parseObject(JSON.toJSONString(obj), ShopDTO.class);
			}
		}else{
			logger.error("web get shop error:{}",apiResponse.getRpMsg());
		}
		return shopInfo;
	}


	public void loginSuccessInitDate(ShopDTO shop,Long userId,String csNick) throws Exception{

		ApiResponse apiResponse=loginBussiness.loginSuccessInitDate(shop,userId,csNick);
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1002.getCode())){
		}else{
			logger.error("loginSuccessInitDate error：{}",apiResponse.getRpMsg());
		}

	}

	private Map<String, Object> handleActivity(Long shopId, String csNick){
		Map<String, Object> data = Maps.newHashMap();
		List<MarketingActivityVO> activityVOLst = Lists.newArrayList();
		data.put("activityFlag", false);

		try {
			//查询当前是否有活动
			List<MarketingActivityDTO> activityLst = marketingActivityBusiness.selectEnableActivity();
			if(!activityLst.isEmpty()){
				for(MarketingActivityDTO activity : activityLst){
					//当前是否在活动时间内
					Date currentDate = new Date();
					boolean timeFlag = currentDate.compareTo(activity.getStartDate()) >= 0 &&
							currentDate.compareTo(activity.getEndDate()) <= 0;

					//当天是否关闭过该活动弹窗
					boolean enableFlag = loginBussiness.getUserOperationLogByNickAndShopAndTime(shopId, new Date(), new Date(), csNick, activity.getActivityName()+PopUpsTypeEnum.ACTIVITY_NOTICE.getType());

					//判断用户版本与订购类型
					boolean versionFlag = false;
					boolean TypeFlag = false;

					List<ShopSubScribe> subLst = marketingActivityBusiness.selectShopSubscribeByShopId(shopId);

					String type;
					String version = "";
					if(!subLst.isEmpty()){
						ShopSubScribe sub = subLst.get(0);
						if((sub.getOrderCycle().equals(-7) || sub.getOrderCycle().equals(-15))){
							type = "1";//1:试用
						}else{
							type = "2";//2:付费
						}

						if("FW_GOODS-908622-1".equals(sub.getItemCode()) || "FW_GOODS-908622-2".equals(sub.getItemCode())){
							version = "1";//1:基础版
						}else if("FW_GOODS-908622-3".equals(sub.getItemCode()) || "FW_GOODS-908622-4".equals(sub.getItemCode())){
							version = "2";//2:高级版
						}

						if("0".equals(activity.getVersion()) || activity.getVersion().equals(version)){//0 代表全部版本
							versionFlag = true;
						}
						if("0".equals(activity.getOrderType()) || activity.getOrderType().equals(type)){//0 代表全部订购类型
							TypeFlag = true;
						}
					}

					//封装参数返回给前端
					if(timeFlag && enableFlag && versionFlag && TypeFlag){
						MarketingActivityVO vo = new MarketingActivityVO();
						vo.setActivityName(activity.getActivityName());
						vo.setActivityContent(activity.getActivityContent());
						vo.setCreateDate(activity.getCreateDate());
						vo.setUrl(activity.getUrl());
						activityVOLst.add(vo);
					}
				}
			}
			//判断是否有弹窗
			if(!activityVOLst.isEmpty()){
				data.put("activityFlag", true);
				data.put("activityLst", activityVOLst);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return data;
	}

	private Map<String, Object> handleActivityV2(Long shopId, String csNick){
		Map<String, Object> data = Maps.newHashMap();
		List<MarketingActivityV2VO> activityVOLst = Lists.newArrayList();
		data.put("activityV2Flag", false);

		try {
			//TODO 查询当前是否有活动 根据时间范围+版本信息
			List<MarketingActivityV3RespDTO> activityLst = marketingActivityOptV3.selectEnableActivity(shopId, csNick, LocalDateTime.now());
			//判断是否有弹窗
			if(!activityLst.isEmpty()){
				data.put("activityV2Flag", true);
				activityVOLst = activityLst.stream().sorted(Comparator.comparing(MarketingActivityV3RespDTO::getActivitySort)).map(t -> {
					MarketingActivityV2VO vo = new MarketingActivityV2VO();
					vo.setActivityName(t.getActivityName());
					vo.setActivityContent(t.getActivityContent());
					vo.setActivityId(t.getId());
					vo.setUrl(t.getUrl());
					vo.setActivitySort(t.getActivitySort());
					vo.setSize(t.getSize());
					vo.setType(t.getType());
					return vo;
				}).collect(Collectors.toList());
				data.put("activityV2Lst", activityVOLst);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return data;
	}

	private LoginResultBO commonLogin(LoginUserParam userParam,String switchFlag, ShopInfoDO shopInfo,Integer flag) throws Exception{

		LoginResultBO loginResult = new LoginResultBO(1);
		ApiResponse apiResponse = loginBussiness.commonLogin(userParam, switchFlag, shopInfo,flag);
		if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1002.getCode())) {
			Object retCodebj = apiResponse.getData().get("retCode");
			Object msgObj = apiResponse.getData().get("msg");
			loginResult.setRetCode((int) retCodebj);
			loginResult.setErrMsg(String.valueOf(msgObj));
			Object sessionMapObj = apiResponse.getData().get("sessionMap");
			if (sessionMapObj != null) {
				Map<String, Object> sessionMap = JacksonUtils.json2map(JSON.toJSONString(sessionMapObj));
				loginResult.setSessionMap(sessionMap);
			}else{
				logger.info("pin:{} get sessionMapObj is empty",userParam.getPin());
			}
		} else {
			logger.error("pin :{} commlogin  error:{}",userParam.getPin(), apiResponse.getRpMsg());
		}
		return loginResult;
	}

	private String setLoginInfoSession(String openId,Map<String, Object> sessionMap){
		//			生成 token 并保存在 Redis 中
		String token = KeyUtils.genUniqueKey();
		final String tokenKey =  JwtConstants.TOKEN_SESSION + token;
		System.out.println("setLoginInfoSession tokey---------"+tokenKey);
		if(sessionMap != null){
			sessionMap.put("sysAdminFlag",2);//登录状态：1：管理员后台登录，2：用户正常登录
			sessionMap.put("openId",openId);
			Object mainUser =sessionMap.get("mainUser");
			if (mainUser == null) {
				return null;
			} else {
				ShopUserDTO shopUserDTO = JSON.parseObject(JSON.toJSONString(mainUser), ShopUserDTO.class);
				logger.info("visitorCheck:{}",shopUserDTO.getVistorCheck());
				if(shopUserDTO.getMainAccount()!=null&&shopUserDTO.getVistorCheck()!=null){
					sessionMap.put("authVistorFlag",shopUserDTO.getMainAccount() && shopUserDTO.getVistorCheck()? 1: 2);//是否显示访问密码 1：显示 2：不显示
				}
			}
//			将token存储在 Redis 中。键是 TOKEN_用户id, 值是token
			redisOperator.putAll(tokenKey, sessionMap);
			redisOperator.expire(tokenKey,12*60, TimeUnit.MINUTES);
		}
		return token;
	}


	private ShopDTO getMainShopMemberShop(Long mainShopId,Long memberShopId) throws Exception{
		MasterServiceShopQuery shop = this.getMasterServiceShopByParam(String.valueOf(mainShopId));
		ApiResponse apiResponse=loginBussiness.getMainShopMemberShop(shop.getMainShop(), memberShopId);
		ShopDTO selectShop=null;
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			Object obj=apiResponse.getData().get("selectShop");
			if(obj!=null){
				selectShop=JSON.parseObject(JSON.toJSONString(obj), ShopDTO.class);
			}
		}else{
			logger.error("getMainShopMemberShop:{}",apiResponse.getRpMsg());
		}
		return selectShop;
	}

	private Boolean initializationFlag(Long shopId) throws Exception{
		Boolean initFlag = false;
		ApiResponse apiResponse = shopSysSettingBusiness.selectCsCountByShopIdByType(shopId, null);
		if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
			Object obj = apiResponse.getData().get("count");
			if (obj != null) {
				int count =(int) obj;
				if (count > 0) {
					initFlag = true;
				}
			}
		}
		return initFlag;
	}

	/**
	 *web 调用登陆日志
	 * @param request
	 * @param deviceId
	 * @param code 0为登陆成功否则就是登陆失败
	 * @param msg 登陆失败返回信息
	  @param loginTime 登陆时间
	 * @return
	 */
	@RequestMapping("/uploadLoginLog")
	@ResponseBody
	public Object uploadLoginLog(HttpServletRequest request,String nick,String shopId,String deviceId,Integer code,String msg,Long loginTime){
		if(StringUtils.isBlank(nick)||StringUtils.isBlank(deviceId)){
			logger.info("上传登陆日志参数为空 nick:{},deviceId:{},shopId:{}",nick,deviceId,shopId);
			return null;
		}
		String tokenKey = getRequestCookie();
		redisOperator.put(tokenKey,"deviceId",deviceId);
		ShopQuery shop= null;
		String shopName="";
		try {
			if(shopId!=null){
				shop = shopSysManagerBusiness.getShopInfo(Long.valueOf(shopId));
				shopName=shop.getTitle();
			}
			uploadLog(request,nick,shopName,code,msg,deviceId,loginTime);
			logger.info("web upload LoginLog success");
		} catch (Exception e) {
			logger.error("web upload Login Log error:{}",e.getMessage(),e);
		}
		return null;
		}


	/**
	 *  插件端调用日志
	 * @param request
	 * @param deviceId
	 * @param code 0为登陆成功否则就是登陆失败
	 * @param msg 登陆失败返回信息
	 @param loginTime 登陆时间
	  * @return
	 */
	@RequestMapping("/uploadLoginLogForPlugin")
	@ResponseBody
	public Object uploadLoginLogForPlugin(HttpServletRequest request,String nick,String shopId,String deviceId,Integer code,String msg,Long loginTime){
		if(StringUtils.isBlank(nick)||StringUtils.isBlank(deviceId)){
			logger.info("上传登陆日志参数为空 nick:{},deviceId:{},shopId:{}",nick,deviceId,shopId);
			return null;
		}
		ShopQuery shop;
		String shopName="";
		try {
			if(shopId!=null){
				shop = shopSysManagerBusiness.getShopInfo(Long.valueOf(shopId));
                if (shop == null) {
                    return null;
                }
				shopName=shop.getTitle();
			}
			uploadLog(request,nick,shopName,code,msg,deviceId,loginTime);
			logger.info("plugin upload LoginLog success");
		} catch (Exception e) {
			logger.error("plugin upload Login Log error:{}",e.getMessage(),e);
		}
		return null;
	}

	@RequestMapping(value="/getAuthExpiredFlagForPlugin",method=RequestMethod.POST)
	@ResponseBody
	public ApiResponse getAuthExpiredFlagForPlugin(String shopId, String nick) {
		ApiResponse apiResponse;
		Map<String, Object> result = new HashMap<>();
		try {
			Boolean mainAccount = false;
			Date authDeadLine = null;
			List<ShopUserDTO> userLst = loginBussiness.selectUserListByShopId(shopId);
			if(CollectionUtils.isNotEmpty(userLst)){
				for(ShopUserDTO user : userLst){
					if(user.getMainAccount()){
						authDeadLine = user.getAuthDeadLine();
					}
					if(user.getNick().equals(nick)){
						mainAccount = user.getMainAccount();
					}
				}
			}

			//判断用户授权是否过期(当天首次登录时提醒)
			result.put("userAuthExpiredFlag",false);
			//boolean todayFirstLoginFlag = getUserLoginCountByNickAndShopAndTime(Long.valueOf(shopId), nick);
			if(!mainAccount &&  authDeadLine != null && authDeadLine.compareTo(new Date()) <= 0){//authDeadLine 小于当前时间即为失效
				result.put("userAuthExpiredFlag",true);
			}

			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
			logger.error("error information：" + e.getMessage(), e);
		}
		apiResponse.setData(result);
		return apiResponse;
	}

	private void uploadLog(HttpServletRequest request,String nick,String shopName,Integer code,String msg,String deviceId,Long loginTime) throws  Exception{
		String userIp= SecurityMUtil.getIpAddr(request);
		LoginLogUploadParam loginParam=new LoginLogUploadParam();
		loginParam.setAppName(AppConstants.SERVER_PIN);
		loginParam.setJosAppKey(AppConstants.APP_KEY);
		loginParam.setUserId(nick);
		loginParam.setJdId(shopName);
		loginParam.setResult(code);
		loginParam.setMessage(msg);
		loginParam.setUserIp(userIp);
		loginParam.setDeviceId(deviceId);
		loginParam.setTimeStamp(loginTime);
		String loginJson= JSONObject.toJSONString(loginParam);
		logger.info("loginJson:{}",loginJson);
		UploadLogUtil.uploadBatchLog("login",loginParam, AppConstants.APP_KEY,loginTime);


	}
//	public static void main(String[] args) throws UnsupportedEncodingException  {
////		String state="eyJqb3NfcGFyYW1ldGVycyI6eyJ1c2VyX25hbWUiOiJhdXjlrpjml5ct5Y 254K554K5IiwiZW5kX2RhdGUiOjE1NzM0MzcyMDUwMDAsIml0ZW1fY29kZSI6IkZXX0dPT0RTLTkwODYyMi0yIiwiYXJ0aWNsZV9udW0iOjEsInZlcnNpb25fbm8iOjIsImFwcF9rZXkiOiIzMjFDRUFCMDAxRjU5RkREQTY3REMzODhDMDU3NTk1OCJ9fQ==";
////		//System.out.println(new String(Base64.getDecoder().decode(state), "UTF-8"));
////
////		Pattern p = Pattern.compile("\\s");
////        Matcher m = p.matcher(state);
////        if(m.find()){
////        	 String strNoBlank = m.replaceAll("+");
////             System.out.println(strNoBlank);
////             System.out.println(new String(Base64.getDecoder().decode(strNoBlank), "UTF-8"));
////        }
//		try {
//			System.out.println(ApiClientUtil.getSessionKeyByRefreshToken("381b7a0b8cbb4d6fadcd50f333aeba2chmgi"));
//		} catch (HttpReqException e) {
//			e.printStackTrace();
//		} catch (JacksonParseException e) {
//			e.printStackTrace();
//		}
//
//
//	}

	/**
	 * 判断登陆用户是否快到期 给与弹窗提示
	 * @param shopId
	 * @param userNick
	 * @param expiredDay
	 * @return
	 */
	private Map<String, Object> handleOrderDeadLineRemindFlag(Long shopId, String userNick, Long expiredDay){
		Map<String, Object> result = Maps.newHashMap();

		boolean orderDeadLineRemindFlag = false;
		List<ShopSubScribeDTO> shopSubLst = loginBussiness.selectShopSubByshopId(shopId);
//		boolean shopTodayFirstLoginFlag = loginBussiness.getUserOperationLogByNickAndShopAndTime(shopId, new Date(), new Date(), userNick, PopUpsTypeEnum.ORDER_DEAD_LINE_REMIND.getType());
		if(CollectionUtils.isNotEmpty(shopSubLst)){
			//订购类型为试用时，在有效期剩余2天时  每天倒计时弹窗提醒
			if((shopSubLst.get(0).getOrderCycle() == -7 && expiredDay <=2) || (shopSubLst.get(0).getOrderCycle() == -15 && expiredDay <=2)) {
				boolean shopTodayFirstLoginFlag = loginBussiness.getUserOperationLogByNickAndShopAndTime(shopId, new Date(), new Date(), userNick, PopUpsTypeEnum.ORDER_DEAD_LINE_REMIND.getType());
				if (shopTodayFirstLoginFlag) {
					orderDeadLineRemindFlag = true;
				}
				//订购类型为付费时 在剩余有效期7天，每天倒计时弹窗提醒
			}else if(shopSubLst.get(0).getOrderCycle() != -7 && shopSubLst.get(0).getOrderCycle() != -15 && expiredDay <=7){
				boolean shopTodayFirstLoginFlag = loginBussiness.getUserOperationLogByNickAndShopAndTime(shopId, new Date(), new Date(), userNick, PopUpsTypeEnum.ORDER_DEAD_LINE_REMIND.getType());
				if (shopTodayFirstLoginFlag) {
					orderDeadLineRemindFlag = true;
				}
			}else{
				orderDeadLineRemindFlag = this.expirationCheck(shopId, expiredDay, userNick);
			}

//			if(shopSubLst.get(0).getOrderCycle() == -7 && expiredDay <=2 && shopTodayFirstLoginFlag
//					|| shopSubLst.get(0).getOrderCycle() == -15 && expiredDay <=2 && shopTodayFirstLoginFlag){
//				orderDeadLineRemindFlag = true;
//			}else{
//				orderDeadLineRemindFlag = this.expirationCheck(shopId, expiredDay, userNick);
//			}
//			if(shopSubLst.get(0).getOrderCycle() != -7 && shopSubLst.get(0).getOrderCycle() != -15 && expiredDay <=7 && shopTodayFirstLoginFlag){
//				orderDeadLineRemindFlag = true;
//			}
		}
		result.put("orderDeadLineRemindFlag", orderDeadLineRemindFlag);
		return result;
	}

	private Boolean expirationCheck(Long shopId, Long expiredDay, String userNick){
		try {
			List<Integer> list = Arrays.asList(0,1,3,7,15,30,60);
			OptionalInt first = IntStream.range(0, list.size())
					.filter(i -> list.get(i) >= expiredDay)
					.findFirst();
			if(first.isPresent()){
				int startIndex = first.getAsInt();
				LocalDate now = LocalDate.now();
				LocalDateTime startTime = now.atStartOfDay().minusDays(list.get(startIndex));
				LocalDateTime endTime = LocalDateTime.now();
				if(startIndex > 0){
					int endIndex = startIndex - 1;
					endTime = now.atStartOfDay().minusDays(endIndex);
				}
				System.out.println(startTime);
				System.out.println(endTime);
				boolean shopTodayFirstLoginFlag = loginBussiness.getUserOperationLogByNickAndShopAndTime(shopId, Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()), new Date(), userNick, PopUpsTypeEnum.ORDER_DEAD_LINE_REMIND.getType());
				if(shopTodayFirstLoginFlag){
					return Boolean.TRUE;
				}
			}
		}catch (Exception e){
			logger.error("expirationCheck error:{}",e.getMessage(),e);
		}
		return Boolean.FALSE;
	}

	public static void main(String[] args) {
		List<Integer> list = Arrays.asList(0,1,3,7,15,30,60);
		OptionalInt first = IntStream.range(0, list.size())
				.filter(i -> list.get(i) >= 60)
				.findFirst();
		if(first.isPresent()){
			int startIndex = first.getAsInt();
			LocalDate now = LocalDate.now();
			LocalDateTime startTime = now.atStartOfDay().minusDays(list.get(startIndex));
			LocalDateTime endTime = LocalDateTime.now();
			if(startIndex > 0){
				int endIndex = startIndex - 1;
				endTime = now.atStartOfDay().minusDays(endIndex);
			}
			System.out.println(startTime);
			System.out.println(endTime);
//			boolean shopTodayFirstLoginFlag = loginBussiness.getUserOperationLogByNickAndShopAndTime(shopId, Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()), new Date(), userNick, PopUpsTypeEnum.ORDER_DEAD_LINE_REMIND.getType());
//			if(shopTodayFirstLoginFlag){
//				return Boolean.TRUE;
//			}
		}
	}

	/**
	 * 判断是否有上线通知 给与弹窗提示
	 * @param shopId
	 * @param userNick
	 * @return
	 */
	private Map<String, Object> handleOnlineNoticeFlag(Long shopId, String userNick){
		Map<String, Object> result = Maps.newHashMap();

		boolean onlineNoticeFlag = false;
		List<OnlineNoticeDTO> onlineNoticeDTOLst = onlineNoticeBusiness.selectEnableNotice();
		if(!onlineNoticeDTOLst.isEmpty()){
			OnlineNoticeDTO onlineNoticeDTO = onlineNoticeDTOLst.get(0);
			//查询是否有关闭弹窗的操作日志
			boolean a = loginBussiness.getUserOperationLog(shopId, onlineNoticeDTO.getCreateDate(), new Date(), userNick, onlineNoticeDTO.getOnlineVersion()+PopUpsTypeEnum.ONLINE_NOTICE.getType());
			//查询该版本从上线到当前时间是否登录
			boolean b = loginBussiness.getUserLoginCountByNickAndShopAndTime(shopId, onlineNoticeDTO.getCreateDate(), new Date(), userNick);

			if(a && b){
				onlineNoticeFlag = true;
				result.put("onlineVersion", onlineNoticeDTO.getOnlineVersion());
				result.put("onlineNoticeContent", onlineNoticeDTO.getNoticeContent());
			}
		}

		result.put("onlineNoticeFlag", onlineNoticeFlag);
		return result;
	}

	/**
	 * 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址;
	 *
	 * @param request
	 * @return
	 * @throws IOException
	 */
	public final static String getIpAddress(HttpServletRequest request) throws IOException {
		// 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址

		String ip = request.getHeader("X-Forwarded-For");

		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getHeader("Proxy-Client-IP");
			}
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getHeader("WL-Proxy-Client-IP");
			}
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getHeader("HTTP_CLIENT_IP");
			}
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getHeader("HTTP_X_FORWARDED_FOR");
			}
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getRemoteAddr();
			}
		} else if (ip.length() > 15) {
			String[] ips = ip.split(",");
			for (int index = 0; index < ips.length; index++) {
				String strIp = (String) ips[index];
				if (!("unknown".equalsIgnoreCase(strIp))) {
					ip = strIp;
					break;
				}
			}
		}
		return ip;
	}

	private AuthorizationUserInfo decodeStateOfPin(String state) throws  Exception{
		AuthorizationUserInfo user=null;
		if(StringUtils.isNotBlank(state)){
			String states="";
			Pattern p = Pattern.compile("\\s");
			Matcher m = p.matcher(state);
			if(m.find()){
				states = m.replaceAll("+");
			}else{
				states=state;
			}
				logger.info("转换后states:{}",states);
				states= new String(Base64.getDecoder().decode(states), "UTF-8");
				Map<String, AuthorizationUserInfo> userMap = JSONObject.parseObject(states, new TypeReference<Map<String, AuthorizationUserInfo>>() {});
				user =userMap.get("jos_parameters");

		}
		return user;
	}

	@GetMapping(value="/tokenEncryption")
	@ResponseBody
	public ApiResponse tokenEncryption(){
		try {
			ShopDTO mainShop = getMainShop();
			JdClient client = ApiClientUtil.getClient(mainShop.getSessionKey());
			JosIsvTokenEncryptionRequest request=new JosIsvTokenEncryptionRequest();
			request.setTokenStr(mainShop.getSessionKey());
			JosIsvTokenEncryptionResponse response = client.execute(request);
			if(response.getCode().equals("0")){
				return ApiResponse.ofSuccess(response.getReturnType().getData());
			}else{
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001, response.getReturnType().getMsg());
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}
