package com.pes.jd.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.ShopAssessSystemsettingBusiness;
import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.jwt.JwtConstants;
import com.pes.jd.model.DTO.ShopAssessSystemsettingDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestApiResponse;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Controller
public class
BaseController {
//	@Resource
//	protected HttpSession session;

    @Resource
    protected HttpServletRequest request;

    @Resource
    private ShopSysManagerBusiness shopSysManagerBusiness;

    @Resource
    private RedisOperator<String, Object> redisOperator;

    @Resource
    private ShopAssessSystemsettingBusiness shopAssessSystemsettingBusiness;
    public static final String ASSESS_SETTING = "assessSetting";

    public String getRequestCookie() {
        String headerAuthToken = request.getHeader(JwtConstants.AUTH_TOKEN.toLowerCase());
        return JwtConstants.TOKEN_SESSION + headerAuthToken;
    }


    /**
     * @Description:（当前主店铺）
     */
    public ShopDTO getMainShop() throws LoginAuthException {
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "mainShop");
        if (o == null) {
            throw new LoginAuthException();
//			return null;
        } else {
            return JSON.parseObject(JSON.toJSONString(o), ShopDTO.class);
        }

//		ShopDTO shop=new ShopDTO();
//		shop.setShopId(55494L);
//		shop.setSchemaId("pes_jd_rtsub_01");
//		//shop.setSessionKey("cf2b4e90-93ed-44b3-8f72-565f7b7acde5");
//		shop.setDb("DB_01");
//		return shop;
    }

    /**
     * @Description:（当前主账号）
     */
    public ShopUserDTO getMainUser() throws LoginAuthException {
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "mainUser");
        if (o == null) {
//			return null;
            throw new LoginAuthException();
        } else {
            return JSON.parseObject(JSON.toJSONString(o), ShopUserDTO.class);
        }

//		ShopUserDTO user=new ShopUserDTO();
//		user.setUserId(6889213570L);
//		try {
//			user.setNick("AO史密斯崔秀明");
//		} catch (UnsupportedEncodingException e) {
//			e.printStackTrace();
//		}
//		return user;
    }

    /**
     * @Description:（获取登录时的当前店铺系统设置信息）
     */
    public ShopSystemsettingDTO getShopSystemsetting() throws LoginAuthException {
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "systemSetting");
        if (o == null) {
//			return null;
            throw new LoginAuthException();
        } else {
            return JSON.parseObject(JSON.toJSONString(o), ShopSystemsettingDTO.class);
        }
    }

    /**
     * @Description:（当前登录店铺）
     */

    public ShopDTO getCurrentShop() throws LoginAuthException {
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "shop");
        if (o == null) {
//			return null;
            throw new LoginAuthException();
        } else {
            return JSON.parseObject(JSON.toJSONString(o), ShopDTO.class);
        }

//		ShopDTO shop=new ShopDTO();
//		shop.setShopId(55494L);
//		shop.setDb("DB_01");
//		shop.setSchemaId("pes_jd_rtsub_01");
//		//shop.setSessionKey("cf2b4e90-93ed-44b3-8f72-565f7b7acde5");
//		return shop;

    }

    /**
     * @Description:（当前登录用户）
     */
    public ShopUserDTO getCurrentUser() throws LoginAuthException {
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "currentUser");
        if (o == null) {
//			return null;
            throw new LoginAuthException();
        } else {
            return JSON.parseObject(JSON.toJSONString(o), ShopUserDTO.class);
        }
//		ShopUserDTO user=new ShopUserDTO();
//		user.setUserId(6889213570L);
//		try {
//			user.setNick("AO史密斯崔秀明");
//		} catch (UnsupportedEncodingException e) {
//			e.printStackTrace();
//		}
//		return user;
    }

    //判断是否是切换过店铺
    public Boolean isOldShop() {
        //是登陆的店铺
        ShopDTO currentShop;
        ShopDTO mainShop;
        try {
            currentShop = this.getCurrentShop();
            mainShop = this.getMainShop();
        } catch (Exception e) {
            return Boolean.FALSE;
        }
        if (null == currentShop || null == mainShop) {
            return Boolean.FALSE;
        }
        boolean isCurrentShop = currentShop.getShopId().equals(mainShop.getShopId());
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "switchShop");
        if (o == null || isCurrentShop) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    //判断是否是切换过店铺
    public boolean isFirstLoginShop() {
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "switchShop");
        if (o == null) {
            return true;
        }
        return false;
    }

    // -------------------------MasterService 封装业务-------------------------------
    public MasterServiceShopQuery getMasterServiceShopByParam(String shopId) throws LoginAuthException {
        if (Objects.equals(shopId, "null")) {
            shopId = null;
        }
        MasterServiceShopQuery custUser = new MasterServiceShopQuery();
        return getMasterServiceShopByParam(custUser, shopId);
    }

    public MasterServiceShopQuery getMasterServiceShopByParam(MasterServiceShopQuery custUser, String shopIdStr) throws LoginAuthException {
        custUser.setSelectedShop(StringUtils.isBlank(shopIdStr) ? false : true);
        custUser.setCurrentShop(revertShop(this.getCurrentShop(), this.getManagerUser()));
        custUser.setMainShop(revertShop(this.getMainShop(), this.getManagerUser()));
        if (custUser.isSelectedShop()) {
            custUser.setSelectedShop(getSelectedShop(custUser.getMainShop(), Long.valueOf(shopIdStr)));
        }
        return custUser;
    }

    public MasterServiceShopQuery getMasterServiceShopByParam(String shopId, String groupId, String csNick) throws LoginAuthException {
        MasterServiceShopQuery custUser = new MasterServiceShopQuery();

        boolean isSelectedGroup = false;
        boolean isSelectedCs = false;
        if (StringUtils.isBlank(csNick)) {
            if (StringUtils.isNotBlank(groupId)) {// 分组
                isSelectedGroup = true;
            }
        } else {
            isSelectedCs = true;
            groupId = "";
        }
        custUser.setSelectedGroup(isSelectedGroup);
        custUser.setSelectedCs(isSelectedCs);
        custUser.setSelectedShop(StringUtils.isBlank(shopId) ? false : true);
        custUser.setGroupId(groupId);
        custUser.setCsNick(csNick);
        return getMasterServiceShopByParam(custUser, shopId);
    }
    // -------------------------MasterService封装业务-------------------------------

    // -------------------------SubService 封装业务-------------------------------
    public UserShopQuery getCustUserByParam(String shopId) throws LoginAuthException {

        UserShopQuery custUser = new UserShopQuery();
        return getCustUserByParam(custUser, shopId);
    }

    public UserShopQuery getUserShopByParam(String shopId, String groupId, String csNick) throws LoginAuthException {
        UserShopQuery custUser = new UserShopQuery();

        boolean isSelectedGroup = false;
        boolean isSelectedCs = false;
        if (StringUtils.isBlank(csNick)) {
            if (StringUtils.isNotBlank(groupId)) {// 分组
                isSelectedGroup = true;
            }
        } else {
            isSelectedCs = true;
            groupId = "";
        }
        custUser.setSelectedGroup(isSelectedGroup);
        custUser.setSelectedCs(isSelectedCs);
        custUser.setSelectedShop(StringUtils.isBlank(shopId) ? false : true);
        custUser.setGroupId(groupId);
        custUser.setCsNick(csNick);
        return getCustUserByParam(custUser, shopId);
    }

    public UserShopQuery getCustUserByParam(UserShopQuery custUser, String shopIdStr) throws LoginAuthException {
        custUser.setSelectedShop(StringUtils.isBlank(shopIdStr) ? false : true);
        custUser.setCurrentShop(revertShop(this.getCurrentShop(), this.getManagerUser()));
        custUser.setMainShop(revertShop(this.getMainShop(), this.getManagerUser()));
        if (custUser.isSelectedShop()) {
            custUser.setSelectedShop(getSelectedShop(custUser.getMainShop(), Long.valueOf(shopIdStr)));
        }
        return custUser;
    }

    // --------------------------------------------------------
    public ShopQuery getSelectedShop(ShopQuery mainShop, Long shopId) {
        List<ShopQuery> memberShopLst;
        try {
            memberShopLst = shopSysManagerBusiness.selectShopGroupMemberShops(mainShop);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        for (ShopQuery shopQuery : memberShopLst) {
            if (shopQuery.getShopId().equals(shopId)) {
                return shopQuery;
            }
        }
        return null;
    }

    public List<Long> getMemberShopIds(Long shopId) {
        List<Long> memberIds = new ArrayList<>();
        try {
            ShopQuery mainShop = new ShopQuery();
            mainShop.setShopId(shopId);
            List<ShopQuery> memberShopLst = shopSysManagerBusiness.selectShopGroupMemberShops(mainShop);
            for (ShopQuery shopQuery : memberShopLst) {
                memberIds.add(shopQuery.getShopId());
            }
            return memberIds;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public ShopQuery revertShop(ShopDTO shop, ShopUserDTO user) {
        ShopQuery shopQuery = new ShopQuery();
        shopQuery.setShopId(shop.getShopId());
        shopQuery.setSellerId(user.getSellerId());
        shopQuery.setDbName(shop.getDb());
        shopQuery.setSchemaId(shop.getSchemaId());
        shopQuery.setRtSchemaId(shop.getRtSchemaId());
        shopQuery.setRtDbName(shop.getRtDb());
        shopQuery.setSellerNick(user.getNick());
        shopQuery.setSellerShowNick(user.getShowNick());
        shopQuery.setSessionKey(shop.getSessionKey());
        shopQuery.setTitle(shop.getTitle());
        shopQuery.setLastGetDateTime(shop.getPreviousGetDataTime());
        shopQuery.setSubuserNum(shop.getSubuserNum());
        shopQuery.setColType(shop.getColType());
        shopQuery.setOptionSessionKey(shop.getOptionSessionKey());
        return shopQuery;
    }

    public ShopQuery getSelectShop(String shopId) {
        ShopQuery shop = null;
        try {
            shop = shopSysManagerBusiness.getShopInfo(Long.valueOf(shopId));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return shop;
    }

    public static RestApiResponse apiResponse(ApiCodeEnum apiCodeEnum, RestApiResponse baseResponse) {
        baseResponse.of(apiCodeEnum.getCode(), apiCodeEnum.getMsg());
        return baseResponse;
    }

    public static <T> RestApiResponse2<T> apiResponse(ApiCodeEnum apiCodeEnum, RestApiResponse2<T> baseResponse2) {
        baseResponse2.of(apiCodeEnum.getCode(), apiCodeEnum.getMsg());
        return baseResponse2;
    }

    /**
     * @Description:（管理员账号也就是user表的主账号）
     */
    public ShopUserDTO getManagerUser() throws LoginAuthException {
        String tokenKey = getRequestCookie();
        Object o = redisOperator.getHashKey(tokenKey, "manager");
        if (o == null) {
//			return null;
            throw new LoginAuthException();
        } else {
            return JSON.parseObject(JSON.toJSONString(o), ShopUserDTO.class);
        }
    }

    public Object getDeviceId() {
        String tokenKey = getRequestCookie();
        System.out.println("Redis deviceId的token:" + tokenKey);
        Object o = redisOperator.getHashKey(tokenKey, "deviceId");
        if (o == null) {
            return null;
        }
        System.out.println("Redis deviceId的token:" + tokenKey + "deviceId:" + o.toString());
        return o;
    }

    /**
     * 获取平台考核设置
     *
     * @param shopId
     * @return
     */
    public ShopAssessSystemsettingDTO getShopAssessSystemsetting(String shopId) {
        ApiResponse shopAssessSetting = shopAssessSystemsettingBusiness.ShopAssessSystemsettingByShopId(Long.valueOf(shopId));
        return JSONObject.parseObject(JSONObject.toJSONString(shopAssessSetting.getData().get(ASSESS_SETTING)), ShopAssessSystemsettingDTO.class);
    }

    /**
     * 获取用户版本
     *
     * @return 版本号字符串：1-基础版，2-高级版，3-自营版，0-未知版本
     * @throws LoginAuthException
     */
    public Integer getUserVersion() throws LoginAuthException {
        String tokenKey = getRequestCookie();
        String itemCode = (String) redisOperator.getHashKey(tokenKey, "itemCode");
        
        if ("FW_GOODS-908622-1".equals(itemCode) || "FW_GOODS-908622-2".equals(itemCode)) {
            return 1; // 基础版
        } else if ("FW_GOODS-908622-3".equals(itemCode) || "FW_GOODS-908622-4".equals(itemCode)) {
            return 2; // 高级版
        } else if ("FW_GOODS-908622-5".equals(itemCode)) {
            return 3; // 自营版
        }
        return 0; // 未知版本
    }


}
