package com.pes.jd.controller;

import com.pes.jd.config.RedisOperator;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/industryAverage")
public class IndustryAverageController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(IndustryAverageController.class);

    @Resource
    private RedisOperator<String, Object> redisOperator;

    private static final String VERSION_KEY = "industry_avg:version";
    private static final String CS_PREFIX = "industry_avg:cs:";
    private static final String SHOP_PREFIX = "industry_avg:shop:";

    @RequestMapping(value = "/getCustomerServiceAverage", method = RequestMethod.GET)
    public ApiResponse getCustomerServiceAverage() {
        try {
            String userVersion = getUserVersion().toString();
            if ("1".equals(userVersion)) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1003.getCode(), "基础版用户无权限访问此功能");
            }

            String version = getRedisVersion();
            if (version == null) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1002.getCode(), "行业均值数据暂未准备就绪");
            }

            Map<String, Object> result = new HashMap<>();
            
            String shopCountKey = CS_PREFIX + version + ":shop_count";
            Object shopCountObj = redisOperator.get(shopCountKey);
            
            if (shopCountObj == null) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1002.getCode(), "客服行业均值数据不存在");
            }
            
            Integer shopCount = Integer.valueOf(shopCountObj.toString());
            if (shopCount == 0) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1002.getCode(), "客服行业均值数据为空");
            }

            result.put("queryToFinalPaid", calculateAverage(CS_PREFIX + version + ":queryToFinalPaid", shopCount));
            result.put("queryToOutStock", calculateAverage(CS_PREFIX + version + ":queryToOutStock", shopCount));
            result.put("avgRespTimeFirst", calculateAverage(CS_PREFIX + version + ":avgRespTimeFirst", shopCount));
            result.put("avgRespTime", calculateAverage(CS_PREFIX + version + ":avgRespTime", shopCount));
            //result.put("shopCount", shopCount);
            result.put("version", version);

            return new ApiResponse().of(ApiCodeEnum.CODE_SUCCESS_1002.getCode(), "获取客服行业均值成功", result);
            
        } catch (LoginAuthException e) {
            logger.error("获取客服行业均值失败，用户未登录", e);
            return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_HD_01_14.getCode(), "登录超时");
        } catch (Exception e) {
            logger.error("获取客服行业均值失败", e);
            return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1003.getCode(), "获取客服行业均值失败：" + e.getMessage());
        }
    }

    @RequestMapping(value = "/getShopAverage", method = RequestMethod.GET)
    public ApiResponse getShopAverage() {
        try {
            String userVersion = getUserVersion().toString();
            if ("1".equals(userVersion)) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1003.getCode(), "基础版用户无权限访问此功能");
            }

            String version = getRedisVersion();
            if (version == null) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1002.getCode(), "行业均值数据暂未准备就绪");
            }

            Map<String, Object> result = new HashMap<>();
            
            String shopCountKey = SHOP_PREFIX + version + ":shop_count";
            Object shopCountObj = redisOperator.get(shopCountKey);
            
            if (shopCountObj == null) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1002.getCode(), "店铺行业均值数据不存在");
            }
            
            Integer shopCount = Integer.valueOf(shopCountObj.toString());
            if (shopCount == 0) {
                return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1002.getCode(), "店铺行业均值数据为空");
            }

            result.put("pvNum", calculateAverage(SHOP_PREFIX + version + ":pvNum", shopCount));
            result.put("uvNum", calculateAverage(SHOP_PREFIX + version + ":uvNum", shopCount));
            result.put("shopDealPercent", calculateAverage(SHOP_PREFIX + version + ":shopDealPercent", shopCount));
            result.put("shopOutStockPercent", calculateAverage(SHOP_PREFIX + version + ":shopOutStockPercent", shopCount));
            result.put("consultPercent", calculateAverage(SHOP_PREFIX + version + ":consultPercent", shopCount));
            result.put("silenceDealPercent", calculateAverage(SHOP_PREFIX + version + ":silenceDealPercent", shopCount));
            //result.put("shopCount", shopCount);
            result.put("version", version);

            return new ApiResponse().of(ApiCodeEnum.CODE_SUCCESS_1002.getCode(), "获取店铺行业均值成功", result);
            
        } catch (LoginAuthException e) {
            logger.error("获取店铺行业均值失败，用户未登录", e);
            return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_HD_01_14.getCode(), "登录超时");
        } catch (Exception e) {
            logger.error("获取店铺行业均值失败", e);
            return new ApiResponse().of(ApiCodeEnum.CODE_ERROR_1003.getCode(), "获取店铺行业均值失败：" + e.getMessage());
        }
    }

    private String getRedisVersion() {
        Object versionObj = redisOperator.get(VERSION_KEY);
        return versionObj != null ? versionObj.toString() : null;
    }

    private BigDecimal calculateAverage(String key, Integer shopCount) {
        Object totalObj = redisOperator.get(key);
        if (totalObj == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal total = new BigDecimal(totalObj.toString());
        if (shopCount == 0) {
            return BigDecimal.ZERO;
        }

        return total.divide(new BigDecimal(shopCount), 4, RoundingMode.HALF_UP);
    }
}