package com.pes.jd.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.pes.jd.business.*;
import com.pes.jd.data.converter.OrderNotPayConverter;
import com.pes.jd.model.BO.ChatLogBO;
import com.pes.jd.model.DO.CsChatlogDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.AfterOrderParam;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.reponse.ApiResponse;
import com.pes.jd.model.TO.ChatSessionResultTO;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings("Duplicates")
@RestController
@RequestMapping("/handle/")
public class JobCenterController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(JobCenterController.class);

    @Resource
    private ShopManageBusiness shopManageBusiness;

    @Resource
    private JobPriorityTaskBusiness jobPriorityTaskBusiness;

    @Resource
    private DatabaseBusiness databaseBusiness;

    @Resource
    private OrderHandleBussiness orderHandleBussiness;

    @Resource
    private ShopCategoryAndGoodsBussiness shopCategoryAndGoodsBussiness;

    @Resource
    private OrderRefundHandleBussiness orderRefundHandleBussiness;

    @Resource
    private OrderBussiness orderBussiness;

    @Resource
    private OrderNotPayConverter orderNotPayConverter;

    @Resource
    private CsLeaveMsgBusiness csLeaveMsgBusiness;

    @Resource
    private com.pes.jd.data.converter.CsEvalDataConverter csEvalDataConverter;

    @Resource
    private CsTypeDayBusiness csTypeDayBusiness;

    @Resource
    private com.pes.jd.data.api.ChatSessionOperator chatSessionOperator;

    @Resource
    private com.pes.jd.data.converter.ChatLogDateConverter chatLogDateConverter;

    @Resource
    private IndustryAverageCalculateBusiness industryAverageCalculateBusiness;

    /**
     * 拉取和计算店铺数据
     *
     * @param req
     * @return
     */
    @RequestMapping("pullAndCalShopData")
    public Object pullAndCalShopData(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }

        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDate = (String) jo.get("startDate");
        String endDate = (String) jo.get("endDate");
        String isYdStr = (String) jo.get("isYd");

        boolean isYd;
        if (Strings.isNullOrEmpty(isYdStr)) {
            isYd = false;
        }else {
            isYd = Boolean.valueOf(isYdStr);
        }
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);

        final Boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));

                jobPriorityTaskBusiness.pullAndCalShopData(jobShop, jobDate, isDelData,isYd);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", e.getMessage());
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * 计算店铺数据
     *
     * @param req
     * @return
     */
    @RequestMapping("calShopData")
    @ResponseBody
    public Object calShopData(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDate = (String) jo.get("startDate");
        String endDate = (String) jo.get("endDate");

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);

        boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));

                jobPriorityTaskBusiness.calShopData(jobShop, jobDate, isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }


    /**
     * 店铺数据初始化
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "initShopData")
    @ResponseBody
    public Object initShopData(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);// 手动拉取

        JobShopDTO shop = jobShop.getShop();
        if (shop == null) {
            logger.info("查询店铺为空");
            retMap.put("msg", "未查询到店铺");
            return retMap;
        }

        int initDataFlag = shop.getInitDataFlag();
        if (initDataFlag == 0) {
            boolean isDelData = false;

            try {
                Date yestoday = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(new Date(), -1));
                JobDateQuery jobDate = new JobDateQuery(yestoday);
                jobDate.setStartDate(yestoday);
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(yestoday));
                jobPriorityTaskBusiness.initShopData(jobShop, jobDate, isDelData);

            } catch (Exception e) {
                e.printStackTrace();
                logger.error(e.getMessage());
            }

        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * pullShopLoginLogData:(登陆记录拉取). <br/>
     *
     * @param
     * @return Object
     */
    @RequestMapping(value = "pullShopLoginLogData", method = RequestMethod.POST)
    @ResponseBody
    public Object pullShopLoginLogData(HttpServletRequest req) {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDate = (String) jo.get("startDate");
        String endDate = (String) jo.get("endDate");
        String dataTypeStr = (String) jo.get("dataType");

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        Date sDate = null;
        Date eDate = null;
        Integer dataType = null;
        try {
            if (StringUtils.isNoneBlank(dataTypeStr)) {
                dataType = Integer.parseInt(dataTypeStr);
            }
            sDate = DateUtil.getStartDateFromDateStr(startDate);
            eDate = DateUtil.getEndDateFromDateStr(endDate);
        } catch (ParseException e1) {
            e1.printStackTrace();
        }
        int shopIndex = 1;
        boolean isDelData = true;
        String type = "";
        try {

            JobDateQuery jobDate = new JobDateQuery(sDate);
            jobDate.setStartDate(sDate);
            jobDate.setEndDate(DateUtil.getEndTimeOfDate(eDate));

            jobPriorityTaskBusiness.pullShopLoginLogData(jobShop, jobDate, isDelData);
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * createShopAllTable:(每个店铺初始化一套表). <br/>
     *
     * @param
     * @param
     * @param
     * @return
     * @throws IOException
     * <AUTHOR>
     */
    @RequestMapping("createShopAllTable")
    @ResponseBody
    public Object createShopAllTable(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        try {
            databaseBusiness.initCreateShopAllTable(jobShop);
        } catch (Exception e) {
            e.printStackTrace();
        }
        retMap.put("retCode", 0);
        return retMap;
    }


    /**
     * @Description:(作用)
     * @param:@param shopId
     * @param:@param startDate
     * @param:@param endDate
     * @param:@return
     * @return:Object
     * @author:Lsp
     * @date:2018年11月8日
     * @version:V1.8
     */
    @RequestMapping("pullShopOrderData")
    public Object pullShopOrderData(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                // 交易订单
                orderHandleBussiness.pullShopOrderInfo(jobShop, jobDate, true);

				// 增量订单
				orderHandleBussiness.pullShopIncrementOrder(jobShop, jobDate, true);
			}
		}catch (Exception e) {
			e.printStackTrace();
			return "拉取店铺订单失败";
		}
		return "拉取店铺订单成功";
	}


	/**
	 *
	 * @Description:(拉取订单的出库信息)
	 * @param:@param req
	 * @param:@return
	 * @return:Object
	 * @author:Lsp
	 * @date:2019年1月18日
	 * @version:V1.8
	 */
	@RequestMapping(value = "pullOrderOutStockTime")
	@ResponseBody
	public ApiResponse pullOrderOutStockTime(Long shopId,Integer colType,String sessionKey,String sellerNick,String rtSchemaId,String schemaId,Integer venderId, Long orderId, String orderCreatedTime) {
//		logger.info("task-dispatching pullOrderOutStockTime request start");
//		logger.info("venderId:{},orderId:{},orderCreatedTime:{}",venderId, orderId, orderCreatedTime);
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
//		logger.info("sessionKey:{}", shop.getSessionKey());
        try {
            jobPriorityTaskBusiness.pullOrderOutStockTime(shop, orderId, orderCreatedTime);
        } catch (Exception e) {
            logger.error("拉取订单的出库信息 error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_OS_01_01);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
    }


    @RequestMapping("pullShopCategory")
    public Object pullShopCategory(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopCategory(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺类目失败";
        }
        return "拉取店铺类目成功";
    }


    @RequestMapping("pullShopSku")
    public Object pullShopSku(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopSku(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺sku失败";
        }
        return "拉取店铺sku成功";
    }


    @RequestMapping("pullShopGood")
    public Object pullShopGood(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopGood(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺商品失败";
        }
        return "拉取店铺商品成功";
    }




    @RequestMapping("pullOrderRefundData")
    public Object pullOrderRefundData(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                orderRefundHandleBussiness.pullOrderRefundApplyData(jobShop, jobDate, true);
                orderRefundHandleBussiness.pullOrderRefundCheckData(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺退款数据失败";
        }
        return "拉取店铺退款数据成功";
    }

    @RequestMapping(value = "pullOrderCancel")
    @ResponseBody
    public ApiResponse pullOrderCancel(Long shopId, Integer colType, String sessionKey, String sellerNick, String schemaId, Integer venderId, Long orderId, String orderCreatedTime) {
//		logger.info("pullOrderCancel orderId:{}", orderId);
		JobShopDTO shop = new JobShopDTO();
		shop.setShopId(shopId);
		shop.setColType(colType);
		shop.setSessionKey(sessionKey);
		shop.setSellerNick(sellerNick);
		shop.setSchemaId(schemaId);
//		logger.info("sessionKey:{}", shop.getSessionKey());
        try {
			return jobPriorityTaskBusiness.pullOrderCancel(shop, orderId, orderCreatedTime);
        } catch (Exception e) {
			return getReturnInfo(false, "no search order", "-1");
        }
    }

    @RequestMapping(value = "pullOrderCreated")
    @ResponseBody
    public ApiResponse pullOrderCreate(Long shopId, Integer colType, String sessionKey, String sellerNick, String schemaId, Integer venderId, Long orderId, String orderCreatedTime) {
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
//        logger.info("pullOrderCreate=========JobShopDTO================{}",shop);
        try {
            int count = jobPriorityTaskBusiness.pullOrderCreate(shop, orderId, orderCreatedTime);
            if(count==0) {
            	  return getReturnInfo(false, "search order", "0");
            }
        } catch (Exception e) {
            logger.error("pullOrderCreate{}",e.getMessage());
            return getReturnInfo(false, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }

    @RequestMapping(value = "pullOrderPay")
    @ResponseBody
    public ApiResponse pullOrderPay(Long shopId,Integer colType,String sessionKey,String sellerNick,String schemaId,Integer venderId,Long orderId, String orderCreatedTime) {
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
        try {
        	 int count = jobPriorityTaskBusiness.pullOrderPay(shop, orderId, orderCreatedTime);
        	  if(count==0) {
            	  return getReturnInfo(false, "search order", "0");
            }

        } catch (Exception e) {
            logger.error("pullOrderPay{}",e.getMessage());
            return getReturnInfo(false, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }

    @RequestMapping(value = "pullOrderFinish")
    @ResponseBody
    public ApiResponse pullOrderFinish(Long shopId, Integer colType,String sessionKey,String sellerNick,String schemaId,Integer venderId, Long orderId, String orderCreatedTime) {
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
        try {
        	 int count =jobPriorityTaskBusiness.pullOrderFinish(shop, orderId, orderCreatedTime);
            if(count==0) {
          	  return getReturnInfo(false, "search order", "0");
          }
        } catch (Exception e) {
            logger.error("pullOrderFinish{}",e.getMessage());
            return getReturnInfo(false, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }
    @RequestMapping(value = "pullOrderNotPay")
    @ResponseBody
    public  ApiResponse pullOrderNotPay(Long shopId, Integer colType, String sessionKey, String sellerNick, String schemaId, Integer venderId, Long orderId, String orderCreatedTime){
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);

        try {
            orderNotPayConverter.getOrderNotPay(shop,orderId);
        }catch (Exception e) {
            logger.error("pullOrderNotPay{}",e.getMessage());
            return getReturnInfo(true, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }


    @RequestMapping(value = "/getAfterSaleOrder")
    public Object getAfterSaleOrder(@RequestParam(required = true) String afterOrderParam) {
//        System.out.println("getAfterSaleOrder come in");
        long l = System.currentTimeMillis();

        ApiResponse apiResponse = new ApiResponse();
        Map<String, Object> data = new HashMap<String, Object>();
        try {
            AfterOrderParam afterOrderParams = JacksonUtils.json2pojo(afterOrderParam, AfterOrderParam.class);
//            logger.info("getAfterOrderFromJob schemaId{}:", afterOrderParams.getSchemaId());
//            logger.info("getAfterOrderFromJob shopId{}:", afterOrderParams.getShopId());
            List<String> afterSaleBuyerLst = orderBussiness.selectShopOrderLstByBuyersAndDateForAfterSale(afterOrderParams.getShopId(),
                                                                                                             afterOrderParams.getSchemaId(),
                                                                                                             afterOrderParams.getBuyerLst(),
                                                                                                             afterOrderParams.getStartDate(),
                                                                                                             afterOrderParams.getEndDate());

            data.put("afterSaleBuyerLst", afterSaleBuyerLst);
            apiResponse.setData(data);
            apiResponse.setSuccess(Boolean.TRUE);
//            logger.info("getAfterSaleOrder shopId:{} sunccess", afterOrderParams.getShopId());
        } catch (Exception e) {
            apiResponse.setSuccess(Boolean.FALSE);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_SF_06_01.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_SF_06_01.getMsg());
            logger.error("getAfterSaleOrder error", e);
        }
//        System.out.println("getAfterSaleOrder consume end time :"+(System.currentTimeMillis() - l));
        return apiResponse;
    }


    private ApiResponse getReturnInfo(boolean successFlag, String msg, String code) {
		ApiResponse apiResponse = new ApiResponse();
		apiResponse.setSuccess(successFlag);
		apiResponse.setRpMsg(msg);
		apiResponse.setRpCode(code);
		return apiResponse;
	}


    @RequestMapping("pullShopCategoryV2")
    public Object pullShopCategoryV2(String shopId, String startDate, String endDate) {
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
                Date date = dates.get(0);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopCategoryV2(jobShop, jobDate, true);

        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺类目V2失败";
        }
        return "拉取店铺类目V2成功";
    }


    @RequestMapping("pullConsultationLeaveMsgs")
    public Object pullConsultationLeaveMsgs(String shopId, String startDate, String endDate) {
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取


        try {
            for (Date date : dates) {
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, false);
                csLeaveMsgBusiness.pullCsLeaveMsgInfo(jobShop, jobDate, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取咨询留言失败";
        }
        return "拉取咨询留言成功";
    }

    @RequestMapping("pullCsEvalDetails2")
    public Object pullCsEvalDetails2(String shopId, String startDate, String endDate) {
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        boolean isDelData = false;
        try {
            for (Date date : dates) {
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, false);
                csEvalDataConverter.pullCsEvalDetails2(jobShop, jobDate, jobDate.getDate(), isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取客服评价详情失败";
        }
        return "拉取客服评价详情成功";
    }

    /**
     * 测试 getChatSessionResult2 方法
     * 
     * @param shopId 店铺ID
     * @param csNick 客服昵称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    @RequestMapping("testChatSessionResult2")
    public Object testChatSessionResult2(String shopId, String csNick, String startDate, String endDate) {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误，请使用yyyy-MM-dd格式");
            return retMap;
        }

        try {
            // 获取店铺信息
            JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
            if (jobShop == null || jobShop.getShop() == null) {
                retMap.put("msg", "未找到店铺信息");
                return retMap;
            }

            String sessionKey = jobShop.getShop().getSessionKey();
            if (StringUtils.isBlank(sessionKey)) {
                retMap.put("msg", "店铺sessionKey为空");
                return retMap;
            }

            System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + sessionKey);

            // 调用 getChatSessionResult2 方法
            ChatSessionResultTO result = chatSessionOperator.getChatSessionResult(sessionKey, csNick, sDate, eDate);
            
            // 构建返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("chatSessionCount", result.getChatPeerLst().size());
            data.put("apiCallCount", result.getNum());
            data.put("retryCount", result.getRetryNum());
            data.put("chatSessionList", result.getChatPeerLst());
            
            retMap.put("data", data);
            retMap.put("retCode", 0L);
            retMap.put("msg", "测试成功");
            
            logger.info("testChatSessionResult2 成功 - shopId:{}, csNick:{}, sessionCount:{}, apiCallCount:{}, retryCount:{}", 
                       shopId, csNick, result.getChatPeerLst().size(), result.getNum(), result.getRetryNum());
            
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("testChatSessionResult2 失败 - shopId:{}, csNick:{}, error:{}", shopId, csNick, e.getMessage());
            retMap.put("msg", "调用getChatSessionResult2失败: " + e.getMessage());
            return retMap;
        }

        return retMap;
    }

    /**
     * 测试 getChatLogs2 方法
     * 
     * @param shopId 店铺ID
     * @param csNick 客服昵称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    @RequestMapping("testGetChatLogs2")
    public Object testGetChatLogs2(String shopId, String csNick, String startDate, String endDate) {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误，请使用yyyy-MM-dd格式");
            return retMap;
        }

        try {
            // 获取店铺信息
            JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
            if (jobShop == null || jobShop.getShop() == null) {
                retMap.put("msg", "未找到店铺信息");
                return retMap;
            }

            String sessionKey = jobShop.getShop().getSessionKey();
            if (StringUtils.isBlank(sessionKey)) {
                retMap.put("msg", "店铺sessionKey为空");
                return retMap;
            }

            System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + sessionKey);

            // 调用 getChatLogs2 方法
            ChatLogBO chatLogBo2 = chatLogDateConverter.getChatLogs2(sessionKey, sDate, eDate, csNick);
            ChatLogBO chatLogBo = chatLogDateConverter.getChatLogs(sessionKey, sDate, eDate, csNick);
            
            // 比较两个chatLogBo的数据差异
            compareChatLogData(chatLogBo2, chatLogBo);
            
            // 构建返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("chatLogCount", chatLogBo.getChatLogLst().size());
            data.put("apiCallCount", chatLogBo.getNum());
            data.put("retryCount", chatLogBo.getRetryNum());
            data.put("chatLogList", chatLogBo.getChatLogLst());
            
            retMap.put("data", data);
            retMap.put("retCode", 0L);
            retMap.put("msg", "测试成功");
            
            logger.info("testGetChatLogs2 成功 - shopId:{}, csNick:{}, chatLogCount:{}, apiCallCount:{}, retryCount:{}", 
                       shopId, csNick, chatLogBo.getChatLogLst().size(), chatLogBo.getNum(), chatLogBo.getRetryNum());
            
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("testGetChatLogs2 失败 - shopId:{}, csNick:{}, error:{}", shopId, csNick, e.getMessage());
            retMap.put("msg", "调用getChatLogs2失败: " + e.getMessage());
            return retMap;
        }

        return retMap;
    }

    /**
     * 比较两个ChatLogBO中的chatLogLst数据差异
     */
    private void compareChatLogData(ChatLogBO chatLogBo1, ChatLogBO chatLogBo2) {
        List<CsChatlogDO> list1 = chatLogBo1.getChatLogLst();
        List<CsChatlogDO> list2 = chatLogBo2.getChatLogLst();
        
        System.out.println("========== 开始比较ChatLog数据差异 ==========");
        System.out.println("chatLogBo1 (getChatLogs2) 数据量: " + (list1 != null ? list1.size() : 0));
        System.out.println("chatLogBo2 (getChatLogs) 数据量: " + (list2 != null ? list2.size() : 0));
        
        // 如果有一个为空，直接返回
        if (list1 == null && list2 == null) {
            System.out.println("两个列表都为空，无需比较");
            return;
        }
        if (list1 == null) {
            System.out.println("chatLogBo1的chatLogLst为空，chatLogBo2有 " + (list2 != null ? list2.size() : 0) + " 条数据");
            return;
        }
        if (list2 == null) {
            System.out.println("chatLogBo2的chatLogLst为空，chatLogBo1有 " + list1.size() + " 条数据");
            return;
        }
        
        // 按sid分组
        Map<String, List<CsChatlogDO>> sidMap1 = groupBySid(list1);
        Map<String, List<CsChatlogDO>> sidMap2 = groupBySid(list2);
        
        System.out.println("chatLogBo1 按sid分组后有 " + sidMap1.size() + " 个会话");
        System.out.println("chatLogBo2 按sid分组后有 " + sidMap2.size() + " 个会话");
        
        // 比较sid差异
        Set<String> allSids = new HashSet<>();
        allSids.addAll(sidMap1.keySet());
        allSids.addAll(sidMap2.keySet());
        
        int missingSidCount = 0;
        int differentChatCount = 0;
        
        for (String sid : allSids) {
            List<CsChatlogDO> chats1 = sidMap1.get(sid);
            List<CsChatlogDO> chats2 = sidMap2.get(sid);
            
            if (chats1 == null) {
                System.out.println("sid [" + sid + "] 在chatLogBo1中缺失，chatLogBo2中有 " + chats2.size() + " 条聊天记录");
                missingSidCount++;
                continue;
            }
            
            if (chats2 == null) {
                System.out.println("sid [" + sid + "] 在chatLogBo2中缺失，chatLogBo1中有 " + chats1.size() + " 条聊天记录");
                missingSidCount++;
                continue;
            }
            
            // 比较同一个sid下的聊天记录数量
            if (chats1.size() != chats2.size()) {
                System.out.println("sid [" + sid + "] 聊天记录数量不同: chatLogBo1有 " + chats1.size() + " 条，chatLogBo2有 " + chats2.size() + " 条");
                differentChatCount++;
                
                // 找出缺少的聊天记录
                if (chats1.size() > chats2.size()) {
                    System.out.println("  chatLogBo2缺少的聊天记录:");
                    for (int i = chats2.size(); i < chats1.size(); i++) {
                        System.out.println("    缺少: " + formatChatLogDO(chats1.get(i)));
                    }
                } else {
                    System.out.println("  chatLogBo1缺少的聊天记录:");
                    for (int i = chats1.size(); i < chats2.size(); i++) {
                        System.out.println("    缺少: " + formatChatLogDO(chats2.get(i)));
                    }
                }
            } else {
                // 数量相同，比较每条记录的内容
                boolean hasContentDiff = false;
                for (int i = 0; i < chats1.size(); i++) {
                    CsChatlogDO chat1 = chats1.get(i);
                    CsChatlogDO chat2 = chats2.get(i);
                    
                    if (!isSameChatLog(chat1, chat2)) {
                        if (!hasContentDiff) {
                            System.out.println("sid [" + sid + "] 聊天记录内容有差异:");
                            hasContentDiff = true;
                        }
                        System.out.println("  第" + (i+1) + "条记录不同: " + getChatLogDiff(chat1, chat2));
                        differentChatCount++;
                    }
                }
            }
        }
        
        System.out.println("比较完成:");
        System.out.println("  缺失的sid数量: " + missingSidCount);
        System.out.println("  内容不同的会话数量: " + differentChatCount);
        System.out.println("========== ChatLog数据差异比较结束 ==========");
    }
    
    /**
     * 按sid分组聊天记录
     */
    private Map<String, List<CsChatlogDO>> groupBySid(List<CsChatlogDO> chatLogs) {
        Map<String, List<CsChatlogDO>> sidMap = new HashMap<>();
        for (CsChatlogDO chat : chatLogs) {
            String sid = chat.getSid();
            if (sid == null) {
                sid = "null_sid"; // 处理sid为null的情况
            }
            sidMap.computeIfAbsent(sid, k -> new ArrayList<>()).add(chat);
        }
        return sidMap;
    }
    
    /**
     * 比较两个聊天记录是否相同
     */
    private boolean isSameChatLog(CsChatlogDO chat1, CsChatlogDO chat2) {
        return equals(chat1.getId(), chat2.getId()) &&
               equals(chat1.getShopId(), chat2.getShopId()) &&
               equals(chat1.getCsNick(), chat2.getCsNick()) &&
               equals(chat1.getBuyer(), chat2.getBuyer()) &&
               equals(chat1.getTime(), chat2.getTime()) &&
               equals(chat1.getContent(), chat2.getContent()) &&
               equals(chat1.getDirection(), chat2.getDirection()) &&
               equals(chat1.getLength(), chat2.getLength()) &&
               equals(chat1.getType(), chat2.getType()) &&
               equals(chat1.getSid(), chat2.getSid()) &&
               equals(chat1.getSkuId(), chat2.getSkuId()) &&
               equals(chat1.getMt(), chat2.getMt());
    }
    
    /**
     * 获取两个聊天记录的差异信息
     */
    private String getChatLogDiff(CsChatlogDO chat1, CsChatlogDO chat2) {
        StringBuilder diff = new StringBuilder();
        
        if (!equals(chat1.getId(), chat2.getId())) {
            diff.append("id不同[").append(chat1.getId()).append(" vs ").append(chat2.getId()).append("] ");
        }
        if (!equals(chat1.getShopId(), chat2.getShopId())) {
            diff.append("shopId不同[").append(chat1.getShopId()).append(" vs ").append(chat2.getShopId()).append("] ");
        }
        if (!equals(chat1.getCsNick(), chat2.getCsNick())) {
            diff.append("csNick不同[").append(chat1.getCsNick()).append(" vs ").append(chat2.getCsNick()).append("] ");
        }
        if (!equals(chat1.getBuyer(), chat2.getBuyer())) {
            diff.append("buyer不同[").append(chat1.getBuyer()).append(" vs ").append(chat2.getBuyer()).append("] ");
        }
        if (!equals(chat1.getTime(), chat2.getTime())) {
            diff.append("time不同[").append(chat1.getTime()).append(" vs ").append(chat2.getTime()).append("] ");
        }
        if (!equals(chat1.getContent(), chat2.getContent())) {
            diff.append("content不同[").append(truncateString(chat1.getContent(), 50)).append(" vs ").append(truncateString(chat2.getContent(), 50)).append("] ");
        }
        if (!equals(chat1.getDirection(), chat2.getDirection())) {
            diff.append("direction不同[").append(chat1.getDirection()).append(" vs ").append(chat2.getDirection()).append("] ");
        }
        if (!equals(chat1.getLength(), chat2.getLength())) {
            diff.append("length不同[").append(chat1.getLength()).append(" vs ").append(chat2.getLength()).append("] ");
        }
        if (!equals(chat1.getType(), chat2.getType())) {
            diff.append("type不同[").append(chat1.getType()).append(" vs ").append(chat2.getType()).append("] ");
        }
        if (!equals(chat1.getSid(), chat2.getSid())) {
            diff.append("sid不同[").append(chat1.getSid()).append(" vs ").append(chat2.getSid()).append("] ");
        }
//        if (!equals(chat1.getSkuId(), chat2.getSkuId())) {
//            if(chat1.getMt() == 0 && chat2.getMt() ==null ){
//
//            }else{
//                diff.append("skuId不同[").append(chat1.getSkuId()).append(" vs ").append(chat2.getSkuId()).append("] ");
//            }
//
//        }
        if (!equals(chat1.getMt(), chat2.getMt())) {

                diff.append("mt不同[").append(chat1.getMt()).append(" vs ").append(chat2.getMt()).append("] ");


        }
        
        return diff.toString();
    }
    
    /**
     * 安全的对象比较方法，处理null值
     */
    private boolean equals(Object obj1, Object obj2) {
        if (obj1 == null && obj2 == null) {
            return true;
        }
        if (obj1 == null || obj2 == null) {
            return false;
        }
        return obj1.equals(obj2);
    }
    
    /**
     * 格式化CsChatlogDO对象为字符串
     */
    private String formatChatLogDO(CsChatlogDO chat) {
        if (chat == null) {
            return "null";
        }
        return String.format("CsChatlogDO[id=%d, buyer=%s, csNick=%s, time=%s, content=%s]", 
                            chat.getId(), chat.getBuyer(), chat.getCsNick(), 
                            chat.getTime(), truncateString(chat.getContent(), 30));
    }
    
    /**
     * 截断字符串，避免打印过长的内容
     */
    private String truncateString(String str, int maxLength) {
        if (str == null) {
            return "null";
        }
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength) + "...";
    }

    /**
     * 单店铺客服行业均值计算测试接口
     * @param shopId 店铺ID
     * @return 计算结果
     */
    @RequestMapping("testCsIndustryAverageCalculateForShop")
    public Object testCsIndustryAverageCalculateForShop(Long shopId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (shopId == null) {
                result.put("success", false);
                result.put("message", "shopId 不能为空");
                return result;
            }
            
            logger.info("开始测试单店铺客服行业均值计算功能，shopId: {}", shopId);
            long startTime = System.currentTimeMillis();
            
            // 获取店铺信息
            JobShopQuery jobShop = shopManageBusiness.getJobShop(shopId, 1);
            if (jobShop == null) {
                result.put("success", false);
                result.put("message", "未找到店铺信息: " + shopId);
                return result;
            }
            
            // 执行单店铺客服行业均值计算
            industryAverageCalculateBusiness.calculateAndUpdateCsIndustryAverageForSingleShop(jobShop);
            
            long endTime = System.currentTimeMillis();
            long consumeTime = endTime - startTime;
            
            result.put("success", true);
            result.put("message", "单店铺客服行业均值计算测试完成");
            result.put("shopId", shopId);
            result.put("consumeTime", consumeTime + "ms");
            
            logger.info("单店铺客服行业均值计算测试完成，shopId: {}, 耗时: {}ms", shopId, consumeTime);
            
        } catch (Exception e) {
            logger.error("单店铺客服行业均值计算测试失败，shopId: {}: {}", shopId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "单店铺客服行业均值计算测试失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 单店铺店铺行业均值计算测试接口
     * @param shopId 店铺ID
     * @return 计算结果
     */
    @RequestMapping("testShopIndustryAverageCalculateForShop")
    public Object testShopIndustryAverageCalculateForShop(Long shopId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (shopId == null) {
                result.put("success", false);
                result.put("message", "shopId 不能为空");
                return result;
            }
            
            logger.info("开始测试单店铺店铺行业均值计算功能，shopId: {}", shopId);
            long startTime = System.currentTimeMillis();
            
            // 获取店铺信息
            JobShopQuery jobShop = shopManageBusiness.getJobShop(shopId, 1);
            if (jobShop == null) {
                result.put("success", false);
                result.put("message", "未找到店铺信息: " + shopId);
                return result;
            }
            
            // 执行单店铺店铺行业均值计算
            industryAverageCalculateBusiness.calculateAndUpdateShopIndustryAverageForSingleShop(jobShop);
            
            long endTime = System.currentTimeMillis();
            long consumeTime = endTime - startTime;
            
            result.put("success", true);
            result.put("message", "单店铺店铺行业均值计算测试完成");
            result.put("shopId", shopId);
            result.put("consumeTime", consumeTime + "ms");
            
            logger.info("单店铺店铺行业均值计算测试完成，shopId: {}, 耗时: {}ms", shopId, consumeTime);
            
        } catch (Exception e) {
            logger.error("单店铺店铺行业均值计算测试失败，shopId: {}: {}", shopId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "单店铺店铺行业均值计算测试失败: " + e.getMessage());
        }
        
        return result;
    }

}
