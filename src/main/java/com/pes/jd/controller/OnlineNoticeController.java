package com.pes.jd.controller;

import cn.hutool.core.lang.Assert;
import com.pes.jd.business.OnlineNoticeBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @Author: yuanxun
 * @Date: 15:39 2019/10/14
 * @Description:
 */
@RestController
@RequestMapping("/onlineNotice/")
public class OnlineNoticeController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(OnlineNoticeController.class);

    @Autowired
    private OnlineNoticeBusiness onlineNoticeBusiness;

    @RequestMapping("insert")
    public Object insert(String onlineVersion, String onlineContent) {
        try {
            Assert.notNull(onlineVersion, "onlineVersion not null");
            Assert.notNull(onlineContent, "onlineContent not null");

            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, onlineNoticeBusiness.insert(onlineVersion, onlineContent));
        } catch (Exception e) {
            logger.error("web onlineNotice insert error : {}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1002, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("delete")
    public Object delete(Long id) {
        try {
            Assert.notNull(id, "id not null");

            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, onlineNoticeBusiness.deleteByPrimaryKey(id));
        } catch (Exception e) {
            logger.error("web onlineNotice delete error : {}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1003, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("update")
    public Object update(Long id, String onlineVersion, String noticeContent) {
        try {
            Assert.notNull(id, "id not null");

            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, onlineNoticeBusiness.update(id, onlineVersion, noticeContent));
        } catch (Exception e) {
            logger.error("web onlineNotice update error : {}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1003, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("updateEnable")
    public Object updateEnable(Long id, Boolean enableSwitch) {
        try {
            Assert.notNull(id, "id not null");
            Assert.notNull(enableSwitch, "enableSwitch not null");

            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, onlineNoticeBusiness.updateEnable(id, enableSwitch));
        } catch (Exception e) {
            logger.error("web onlineNotice updateEnable error : {}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1003, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("select")
    public Object select(@RequestParam(name = "startDateStr", required = true) String startDateStr,
                         @RequestParam(name = "endDateStr", required = true) String endDateStr,
                         @RequestParam(name = "version", required = true) String version) {
        try {
            Date startDate = DateUtils.parseYMd(startDateStr);
            Date endDate = DateUtils.parseYMd(endDateStr);

            return onlineNoticeBusiness.selectNoticeByVersionAndDate(startDate, endDate, version);
        } catch (Exception e) {
            logger.error("web onlineNotice select error : {}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1002, RestApiResponse2.of(false));
        }
    }
}
