package com.pes.jd.controller;

import com.pes.jd.business.LoginInterfaceBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/loginInterface")
public class LoginInterfaceController {
    private static final Logger logger = LoggerFactory.getLogger(IndexController.class);

    @Autowired
    private LoginInterfaceBusiness loginInterfaceBusiness;

//    @RequestMapping(value = "getUserInterfaceType")
//    public Object getUserInterfaceType(@RequestParam("nick") String nick) {
//        ApiResponse resultResp;
//        try {
//            Assert.notNull(nick, " nick must be non null ");
//            resultResp = loginInterfaceBusiness.getUserInterfaceType(nick);
//            return resultResp;
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
//        }
//    }

    @RequestMapping(value = "saveUserInterfaceType")
    public Object saveUserInterfaceType(@RequestParam("nick") String nick,
                                   @RequestParam("interfaceType") Integer interfaceType) {
        ApiResponse resultResp;
        try {
            Assert.notNull(nick, " nick must be non null ");
            Assert.notNull(interfaceType, " interfaceType must be non null ");
            resultResp = loginInterfaceBusiness.saveUserInterfaceType(nick, interfaceType);
            return resultResp;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        }
    }
}
