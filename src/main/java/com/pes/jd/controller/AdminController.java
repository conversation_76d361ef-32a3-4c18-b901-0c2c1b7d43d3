package com.pes.jd.controller;

import com.pes.jd.business.AdminBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.JdSystemPageParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Controller
@RequestMapping("/admin/*")
public class AdminController {
	
	private static final Logger logger = LoggerFactory.getLogger(AdminController.class);
	
	@Autowired
	private AdminBusiness adminBusiness;
	
	@RequestMapping(value = "login")
	@ResponseBody
	public ApiResponse login(HttpServletRequest request,@RequestParam(name = "username", required = true) String username,
			@RequestParam(name = "password", required = true) String password){
		ApiResponse apiResponse = null;
		try{
			apiResponse = adminBusiness.adminLogin(username,password,request);
		}catch(Exception e){
			logger.error("后台管理员登录error : {}, {}", e.getMessage(), e);
//			e.printStackTrace();
		}
		return apiResponse;
	}
	
	@RequestMapping(value = "queryShopByAdmin")
	@ResponseBody
	public ApiResponse queryShopByAdmin(@RequestParam(value = "nick", required = true) String nick){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = adminBusiness.queryShopByAdmin(nick);
			logger.info("return informace code:{}", apiResponse.getRpCode());
		} catch (Exception e) {
			logger.error("queryShopByAdmin : error", e.getMessage());
//			e.printStackTrace();
		}
		return apiResponse;
	}
	
	@RequestMapping(value = "sysLoginEntrance")
	@ResponseBody
	public ApiResponse sysLoginEntrance(HttpServletRequest request,@RequestParam(value = "shopId", required = true) String shopId){
		ApiResponse apiResponse = null;
		try {
			apiResponse = adminBusiness.sysLoginEntrance(shopId, request);
			logger.info("return informace code:{}", apiResponse.getRpCode());
		} catch (Exception e) {
			logger.error("sysLoginEntrance : error", e.getMessage());
//			e.printStackTrace();
		}
		return apiResponse;
	}


	/**
	 * 京东可见的系统页
	 * @param nick
	 * @param status
	 * @param startDateStr
	 * @param endDateStr
	 * @return
	 */
	@RequestMapping(value = "queryJdSystemPage")
	@ResponseBody
	public ApiResponse queryJdSystemPage(@RequestParam(value = "nick") String nick,
										 @RequestParam(value = "status") String status,
										 @RequestParam("startDate") String startDateStr,
										 @RequestParam("endDate") String endDateStr,
										 @RequestParam(value = "operateManager",required =false) String operateManager,
										 @RequestParam(value = "ignoreDate",required =false) boolean ignoreDate){
		ApiResponse apiResponse = new ApiResponse();
		Date startDate;
		Date endDate;
		try {
			Assert.notNull(startDateStr,"startDateStr is null");
			Assert.notNull(endDateStr,"endDateStr is null");
			startDate = DateFormatUtils.parseYMd(startDateStr);
			endDate = DateFormatUtils.getEndTimeOfDate(DateFormatUtils.parseYMd(endDateStr));
		}catch (Exception e){
			logger.error("queryShopByJd {}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_02);
		}
		try {
			logger.info("queryJdSystemPage nick={},status={},startDate={},endDate={}",nick,status,startDateStr,endDateStr);
			JdSystemPageParam jdSystemPageParam = new JdSystemPageParam(nick, status, startDate, endDate,ignoreDate, CommonConstants.POP_TYPE,operateManager);
			long s = System.currentTimeMillis();
			apiResponse = adminBusiness.queryJdSystemPage(jdSystemPageParam);
			long e = System.currentTimeMillis();
			logger.info("queryJdSystemPage 查询所有结果花费时间为{}ms",(e-s));
			logger.info("return informace code:{}", apiResponse.getRpCode());
		} catch (Exception e) {
			logger.error("queryShopByAdmin : error", e.getMessage());
//			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 查询所有的dbName和schemaId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "queryEveryServer")
	@ResponseBody
	public ApiResponse queryEveryServer(HttpServletRequest request){
		ApiResponse apiResponse = null;
		try {
			apiResponse = adminBusiness.queryEveryServer(request);
			logger.info("return informace code:{}", apiResponse.getRpCode());
		} catch (Exception e) {
			logger.error("sysLoginEntrance : error", e.getMessage());
		}
		return apiResponse;
	}






}
  
