package com.pes.jd.controller;

import com.pes.jd.business.main.FeatureConversionLogBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.FeatureConversionLogParam;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/featureConversionLog")
public class FeatureConversionLogController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(FeatureConversionLogController.class);

    @Resource
    private FeatureConversionLogBusiness featureConversionLogBusiness;

    @RequestMapping(value = "/addLog", method = RequestMethod.POST)
    public ApiResponse addLog(@RequestBody FeatureConversionLogParam param ) {
        try {
            ApiResponse response = featureConversionLogBusiness.addLog(param);
            logger.info("功能转化日志添加成功: {}", response);
            return response;
        } catch (Exception e) {
            logger.error("功能转化日志添加失败: {}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002, "功能转化日志添加失败: " + e.getMessage());
        }
    }
}