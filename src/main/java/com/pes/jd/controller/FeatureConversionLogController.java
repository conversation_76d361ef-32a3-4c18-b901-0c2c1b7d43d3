package com.pes.jd.controller;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.RequestUrlEnum;
import com.pes.jd.model.Param.FeatureConversionLogParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/featureConversionLog")
public class FeatureConversionLogController extends BaseController{

    private static final Logger logger = LoggerFactory.getLogger(FeatureConversionLogController.class);

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @RequestMapping(value = "/addLog", method = RequestMethod.POST)
    public ApiResponse addLog(@RequestBody FeatureConversionLogParam param) throws LoginAuthException {

        ShopUserDTO currentUser = this.getCurrentUser();
        param.setCsNick(currentUser.getNick());
        param.setShopId(Long.valueOf(currentUser.getShopId()));
        param.setUserLevel(getUserVersion());

        ApiResponse res = null;
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        try {
            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("param", param)
                    .toRequestEntity();
            
            res = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.FEATURE_CONVERSION_LOG_ADD.getName(), body);
            logger.info("功能转化日志添加请求成功: {}", res);
        } catch (Exception e) {
            logger.error("功能转化日志添加失败: {}", e.getMessage(), e);
        }
        return res;
    }
}