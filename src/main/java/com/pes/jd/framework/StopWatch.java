package com.pes.jd.framework;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class StopWatch {

    private String taskName;

    private long startTime;

    private long lastWatchTime;

    private class TaskInfo{

        private String taskName;

        private long startTime;

        private long endTime;

        TaskInfo(String taskName, long startTime, long endTime) {
            this.taskName = taskName;
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }

    private final List<TaskInfo> taskArray = new ArrayList<>();

    public void start(String taskName){
        this.taskName = taskName;
        lastWatchTime = startTime = System.currentTimeMillis();

    }

    public long watch(){
        final long now = System.currentTimeMillis();
        final long res = now - lastWatchTime;
        lastWatchTime = now;
        return res;
    }

    public long watchSeconds(){
        return TimeUnit.MILLISECONDS.toSeconds(watch());
    }

    public long stop(){
        final long endTime = System.currentTimeMillis();
        taskArray.add(new TaskInfo(this.taskName,this.startTime, endTime));
        this.taskName = null;
        final long res = endTime - startTime;
        this.startTime = 0L;
        return res;
    }

    public long stopSeconds(){
        return TimeUnit.MILLISECONDS.toSeconds(stop());
    }


}
