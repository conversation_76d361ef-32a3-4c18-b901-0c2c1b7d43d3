package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.FeatureConversionLogBusiness;
import com.pes.jd.dao.main.FeatureConversionLogDao;
import com.pes.jd.model.DO.FeatureConversionLog;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.FeatureConversionLogParam;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service("featureConversionLogBusiness")
public class FeatureConversionLogBusinessImpl implements FeatureConversionLogBusiness {

    private static final Logger logger = LoggerFactory.getLogger(FeatureConversionLogBusinessImpl.class);

    @Resource
    private FeatureConversionLogDao featureConversionLogDao;

    @Override
    public ApiResponse addLog(FeatureConversionLogParam param) {
        try {
            FeatureConversionLog log = new FeatureConversionLog();
            log.setFeatureLocationId(param.getFeatureLocationId());
            log.setShopId(param.getShopId());
            log.setCsNick(param.getCsNick());
            log.setOptType(param.getOptType());
            log.setUserLevel(param.getUserLevel());
            log.setCreated(new Date());

            int result = featureConversionLogDao.insert(log);
            
            if (result > 0) {
                logger.info("功能转化日志添加成功，记录ID: {}", log.getId());
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
            } else {
                logger.error("功能转化日志添加失败，数据库操作返回0行");
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
            }
        } catch (Exception e) {
            logger.error("功能转化日志添加异常: {}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002, e.getMessage());
        }
    }
}