package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.DealDataAnalysisBusiness;
import com.pes.jd.data.api.VenderShopOperator;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.OrderPreordainDTO;
import com.pes.jd.model.Enum.RequestUrlEnum;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.ms.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.Resource;

/**
 * 成交分析
 */
@Service
public class DealDataAnalysisBusinessImpl implements DealDataAnalysisBusiness {

    private static final Logger logger = LoggerFactory.getLogger(DealDataAnalysisBusinessImpl.class);
    @Resource
    private PopSubRestTemplate popSubRestTemplate;
    @Resource
    private VenderShopOperator venderShopOperator;
    @Override
    public ApiResponse searchShopSaleAnalysisLst(UserShopQuery userShopQuery, ShopSaleParam shopSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopCommonParam", new ShopCommonParam(userShopQuery.getSelectedShop().getShopId(), userShopQuery.getSelectedShop().getSchemaId(), userShopQuery.getSelectedShop().getDbName()))
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .put("shopSaleParam", shopSaleParam)
                .put("sortPageQuery", sortPageQuery)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(userShopQuery.getSelectedShop().getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SHOPSALEANALYSIS.getName(), body);
    }

    @Override
    public ApiResponse searchCsSaleAnalysisLst(UserShopQuery userShopQuery, CsSaleParam csSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) {
        ApiResponse apiResponse = null;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopCommonParam", new ShopCommonParam(userShopQuery.getSelectedShop().getShopId(), userShopQuery.getSelectedShop().getSchemaId(), userShopQuery.getSelectedShop().getDbName()))
                .put("csSaleParam", csSaleParam)
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .put("sortPageQuery", sortPageQuery)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(userShopQuery.getSelectedShop().getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        try {
            apiResponse = popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_CSSALEANALYSIS.getName(), body);
        } catch (HttpClientErrorException e) {
            logger.error(e.getMessage(), e);
        }
        return apiResponse;
    }

    @Override
    public ApiResponse searchSilenceSaleAnalysisLst(UserShopQuery userShopQuery, SilenceSaleParam silenceSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopCommonParam", new ShopCommonParam(userShopQuery.getSelectedShop().getShopId(), userShopQuery.getSelectedShop().getSchemaId(), userShopQuery.getSelectedShop().getDbName()))
                .put("silenceSaleParam", silenceSaleParam)
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .put("sortPageQuery", sortPageQuery)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(userShopQuery.getSelectedShop().getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SILENCESALEANALYSIS.getName(), body);
    }

    @Override
    public ApiResponse selectCsRefundAnalysisLst(ShopQuery shop, RefundAnalysisParam param, OrderInfoLogUploadParam orderInfoLogUploadParam) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shop)
                .put("param", param)
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SELECTREFUNDANALYSISLST.getName(), body);
    }

    @Override
    public ApiResponse selectOrderPresaleLst(UserShopQuery userShopQuery, OrderPresaleParam orderPresaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam){
        ApiResponse apiResponse = null;
        ShopCommonParam shopCommonParam = new ShopCommonParam(userShopQuery.getSelectedShop().getShopId(), userShopQuery.getSelectedShop().getSchemaId(), userShopQuery.getSelectedShop().getDbName());
        try {
            //fix:1125 校验token是否过期
            venderShopOperator.checkShopExpireBySessionKey(userShopQuery.getSelectedShop().getSessionKey());
            shopCommonParam.setSessionKey(userShopQuery.getSelectedShop().getSessionKey());
        } catch (GainShopDataFailException e) {
            if(e.getErrorCode().equals("19")) {
                if (StringUtils.isNotBlank(userShopQuery.getSelectedShop().getOptionSessionKey())) {
                    shopCommonParam.setSessionKey(userShopQuery.getSelectedShop().getOptionSessionKey());
                    logger.info("shopName {} main SessionKey is expire", userShopQuery.getSelectedShop().getTitle());
                } else {
                    logger.info("shopName {} sub SessionKey is empty no update sessionKey", userShopQuery.getSelectedShop().getTitle());
                }
            }
        }catch (Exception e) {
            logger.error("shopName:{} checkShopExpireBySessionKey error:{}",userShopQuery.getSelectedShop().getTitle(),e.getMessage(),e);
        }
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopCommonParam", shopCommonParam)
                .put("orderPresaleParam", orderPresaleParam)
                .put("sortPageQuery", sortPageQuery)
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(userShopQuery.getSelectedShop().getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        try {
            apiResponse = popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SELECTORDERPRESALELST.getName(), body);
        } catch (HttpClientErrorException e) {
            logger.error(e.getMessage(), e);
        }
        return apiResponse;
    }

    @Override
    public ApiResponse selectCsRefundAnalysisLstOfSpu(ShopQuery shop, RefundAnalysisParam param, OrderInfoLogUploadParam orderInfoLogUploadParam) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shop)
                .put("param", param)
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SELECTREFUNDANALYSISLSTOFSPU.getName(), body);
    }


    @Override
    public DataAnalysisVO<OrderPreordainDTO> selectOrderPredainLst(ShopQuery shopQuery, OrderPreOrdainParam orderPreOrdainParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam){
        DataAnalysisVO<OrderPreordainDTO> vo=new DataAnalysisVO<>();

        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),shopQuery.getDbName(),shopQuery.getSessionKey());
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopCommonParam", shopCommonParam)
                .put("orderPreOrdainParam", orderPreOrdainParam)
                .put("sortPageQuery", sortPageQuery)
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        RestResponseTypeRef<DataAnalysisVO<OrderPreordainDTO>> restApi = popSubRestTemplate.postRest(serviceId,RequestUrlEnum.DATA_ANALYSIS_SELECTORDERPREDIAN.getName(),body,new ParameterizedTypeReference<RestResponseTypeRef<DataAnalysisVO<OrderPreordainDTO>>>() {});
        if(restApi!=null&&restApi.getSuccess()){
            vo=restApi.getData();
        }
        return vo;
    }
}
