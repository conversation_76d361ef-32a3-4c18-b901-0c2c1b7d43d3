
package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSON;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.IndustryAverageCalculateBusiness;
import com.pes.jd.business.ShopManageBusiness;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Param.PerformanceParam;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.reponse.ApiResponse;
import com.pes.jd.ms.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.DateFormatUtils;
import com.yiyitech.support.rpc.RestOperator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 行业均值计算业务实现类
 * <AUTHOR> Code
 */
@Service
public class IndustryAverageCalculateBusinessImpl implements IndustryAverageCalculateBusiness {

    private static final Logger logger = LoggerFactory.getLogger(IndustryAverageCalculateBusinessImpl.class);

    @Resource
    private PopSubRestTemplate popSubRestTemplate;

    @Resource
    private RedisOperator redisOperator;

    @Resource
    private UsermgrRestTemplate usermgrRestTemplate;

    // Redis Key 常量 - 版本控制方式
    private static final String REDIS_KEY_VERSION = "industry_avg:version";
    private static final String REDIS_KEY_CS_PREFIX = "industry_avg:cs:";
    private static final String REDIS_KEY_SHOP_PREFIX = "industry_avg:shop:";
    
    // Redis 过期时间：21天（保留3个版本周期）
    private static final long REDIS_EXPIRE_DAYS = 21;

    @Override
    public void calculateAndUpdateCsIndustryAverageForSingleShop(JobShopQuery jobShopQuery) {
        if (jobShopQuery == null || jobShopQuery.getShop() == null) {
            logger.warn("店铺查询对象为空，跳过行业均值计算");
            return;
        }
        
        JobShopDTO shop = jobShopQuery.getShop();
        Long shopId = shop.getShopId();
        
        try {
            logger.debug("开始为店铺 {} 计算行业均值", shopId);
            
            // 1. 获取或创建当前版本
            String currentVersion = getCurrentVersion();
            
            // 2. 检查该店铺是否已参与当前版本的计算
            if (hasShopParticipated(currentVersion, shopId)) {
                logger.debug("店铺 {} 已参与当前版本 {} 的计算，跳过", shopId, currentVersion);
                return;
            }
            
            // 3. 计算店铺绩效数据
            boolean success = processShopDataForVersion(jobShopQuery, currentVersion);
            
            if (success) {
                logger.debug("店铺 {} 行业均值计算成功，版本：{}", shopId, currentVersion);
            } else {
                logger.debug("店铺 {} 行业均值计算跳过（数据不符合要求），版本：{}", shopId, currentVersion);
            }
            
        } catch (Exception e) {
            logger.error("店铺 {} 客服行业均值计算失败: {}", shopId, e.getMessage(), e);
        }
    }

    @Override
    public void calculateAndUpdateShopIndustryAverageForSingleShop(JobShopQuery jobShopQuery) {
        if (jobShopQuery == null || jobShopQuery.getShop() == null) {
            logger.warn("店铺查询对象为空，跳过店铺行业均值计算");
            return;
        }
        
        JobShopDTO shop = jobShopQuery.getShop();
        Long shopId = shop.getShopId();
        
        try {
            logger.debug("开始为店铺 {} 计算店铺行业均值", shopId);
            
            // 1. 获取或创建当前版本（统一版本管理）
            String currentVersion = getCurrentVersion();
            
            // 2. 检查该店铺是否已参与当前店铺版本的计算
            if (hasShopParticipatedInShopAvg(currentVersion, shopId)) {
                logger.debug("店铺 {} 已参与当前店铺版本 {} 的计算，跳过", shopId, currentVersion);
                return;
            }
            
            // 3. 计算店铺绩效数据
            boolean success = processShopDataForShopVersion(jobShopQuery, currentVersion);
            
            if (success) {
                logger.debug("店铺 {} 店铺行业均值计算成功，版本：{}", shopId, currentVersion);
            } else {
                logger.debug("店铺 {} 店铺行业均值计算跳过（数据不符合要求），版本：{}", shopId, currentVersion);
            }
            
        } catch (Exception e) {
            logger.error("店铺 {} 店铺行业均值计算失败: {}", shopId, e.getMessage(), e);
        }
    }



    /**
     * 获取或创建当前版本号
     */
    private String getCurrentVersion() {
        try {
            String currentVersion = (String) redisOperator.get(REDIS_KEY_VERSION);
            
            if (currentVersion == null || isVersionExpired(currentVersion)) {
                // 创建新版本
                currentVersion = generateNewVersion();
                redisOperator.set(REDIS_KEY_VERSION, currentVersion);
                redisOperator.expire(REDIS_KEY_VERSION, REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                
                logger.info("创建新的行业均值计算版本: {}", currentVersion);
                
                // 清理过期版本的数据（可选，为了节省Redis空间）
                cleanupExpiredVersions(currentVersion);
            }
            
            return currentVersion;
        } catch (Exception e) {
            logger.error("获取当前版本失败: {}", e.getMessage(), e);
            return generateNewVersion();
        }
    }
    
    /**
     * 检查版本是否已过期（超过7天）
     */
    private boolean isVersionExpired(String version) {
        try {
            // 版本格式：v20250827
            String dateStr = version.substring(1); // 去掉 'v' 前缀
            LocalDate versionDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDate now = LocalDate.now();
            
            long daysBetween = now.toEpochDay() - versionDate.toEpochDay();
            return daysBetween >= 7;
        } catch (Exception e) {
            logger.error("检查版本过期状态失败: {}", e.getMessage(), e);
            return true; // 异常情况视为过期
        }
    }
    
    /**
     * 生成新版本号
     */
    private String generateNewVersion() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String dateStr =yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return "v" + dateStr;
    }
    
    /**
     * 检查店铺是否已参与当前客服版本的计算
     */
    private boolean hasShopParticipated(String version, Long shopId) {
        try {
            String shopsKey = REDIS_KEY_CS_PREFIX + version + ":shops";
            return redisOperator.isMember(shopsKey, shopId.toString());
        } catch (Exception e) {
            logger.error("检查店铺参与客服版本状态失败: {}", e.getMessage(), e);
            return false; // 异常情况视为未参与
        }
    }
    
    /**
     * 标记店铺已参与客服版本计算
     */
    private void markShopParticipated(String version, Long shopId) {
        try {
            String shopsKey = REDIS_KEY_CS_PREFIX + version + ":shops";
            redisOperator.add(shopsKey, shopId.toString());
            redisOperator.expire(shopsKey, REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("标记店铺参与客服版本状态失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清理过期版本的数据
     */
    private void cleanupExpiredVersions(String currentVersion) {
        // 这里可以添加清理逻辑，删除过期版本的Redis数据
        // 为了简化，先跳过实现
        logger.debug("TODO: 清理过期版本数据，当前版本: {}", currentVersion);
    }
    
    /**
     * 为指定版本处理单个店铺数据
     */
    private boolean processShopDataForVersion(JobShopQuery jobShopQuery, String version) {
        JobShopDTO shop = jobShopQuery.getShop();
        Long shopId = shop.getShopId();

        try {
            logger.debug("开始处理店铺: {} (版本: {})", shopId, version);

            // 构建查询参数
            PerformanceParam param = buildPerformanceParam(jobShopQuery);
            
            // 调用Sub模块获取绩效数据
            ApiResponse response = callSubPerformanceApi(shop, param, version);
            
            if (response == null || !isSuccessResponse(response)) {
                logger.warn("店铺 {} 绩效数据调用失败，跳过", shopId);
                return false;
            }

            // 解析并验证数据
            PerformanceData performanceData = parsePerformanceData(response);
            if (performanceData == null) {
                logger.warn("店铺 {} 绩效数据解析失败，跳过", shopId);
                return false;
            }

            // 检查极端值
            if (hasExtremeValues(performanceData)) {
                logger.debug("店铺 {} 存在极端值，跳过: {}", shopId, performanceData);
                return false;
            }

            // 累加数据到当前版本的Redis
            accumulateDataToVersionedRedis(performanceData, version);
            
            // 标记店铺已参与计算
            markShopParticipated(version, shopId);
            
            logger.debug("店铺 {} 数据处理成功 (版本: {})", shopId, version);
            return true;

        } catch (Exception e) {
            logger.error("处理店铺 {} 数据时发生异常: {}", shopId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 累加数据到指定版本的Redis
     */
    private void accumulateDataToVersionedRedis(PerformanceData data, String version) {
        try {
            String prefix = REDIS_KEY_CS_PREFIX + version + ":";
            
            // 累加各项指标 - 使用优化.md中要求的key名
            if (data.inquiryPaymentRate != null) {
                redisOperator.increment(prefix + "queryToFinalPaid", data.inquiryPaymentRate);
                redisOperator.expire(prefix + "queryToFinalPaid", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }
            
            if (data.inquiryDeliveryRate != null) {
                redisOperator.increment(prefix + "queryToOutStock", data.inquiryDeliveryRate);
                redisOperator.expire(prefix + "queryToOutStock", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }
            
            if (data.firstResponseTime != null) {
                redisOperator.increment(prefix + "avgRespTimeFirst", data.firstResponseTime);
                redisOperator.expire(prefix + "avgRespTimeFirst", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }
            
            if (data.avgResponseTime != null) {
                redisOperator.increment(prefix + "avgRespTime", data.avgResponseTime);
                redisOperator.expire(prefix + "avgRespTime", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }

            // 增加店铺计数
            redisOperator.increment(prefix + "shop_count", 1L);
            redisOperator.expire(prefix + "shop_count", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            
        } catch (Exception e) {
            logger.error("累加客服数据到版本化Redis失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 构建绩效查询参数
     */
    private PerformanceParam buildPerformanceParam(JobShopQuery jobShopQuery) {
        PerformanceParam param = new PerformanceParam();
        
        // 设置需要查询的字段
        param.setProperty(Arrays.asList(
                "queryToFinalPaid",      // 询单-付款转化率
                "avgRespTimeFirst",      // 首次平均响应时间
                "avgRespTime",           // 平均响应时间
                "queryToOutStock"        // 询单-出库转化率
        ));
        
        // 设置基本参数
        param.setNickQuery(false);
        param.setPreDefine(false);
        
        // 获取客服信息
        Map<String, String> saleBefore = new HashMap<>();
        Map<String, String> saleAfter = new HashMap<>();
        
        if (CollectionUtils.isNotEmpty(jobShopQuery.getCsLst())) {
            for (CsDTO cs : jobShopQuery.getCsLst()) {
                if (cs.getType() != null) {
                    // 售前客服
                    saleBefore.put(cs.getNick(), cs.getSimpleName());
                    
                    if (cs.getType().equals((byte) 2)) {
                        // 售后客服
                        saleAfter.put(cs.getNick(), cs.getSimpleName());
                    }
                }
            }
        }
        
        param.setNicks(saleBefore);
        param.setAfterSaleNicks(saleAfter);
        
        // 设置系统设置
        //ShopSystemsettingDTO systemSetting = jobShopQuery.getShopSystemsetting();
        ApiResponse apiResponse = selectShopCsAndSystemSettings(jobShopQuery.getShop().getShopId(), "", "", true);

        if (apiResponse.getData() != null) {
            param.setSysSetting(JSON.toJSONString(apiResponse.getData().get("shopSystemsettingDTO")));
        }
        
        // 设置过滤条件（空）
        param.setFilter(new ArrayList<>());
        param.setFilterTime(new ArrayList<>());
        
        return param;
    }

    /**
     * 调用Sub模块的绩效接口
     */
    private ApiResponse callSubPerformanceApi(JobShopDTO shop, PerformanceParam param, String version) {
        try {
            String uri = "/cs/performance/select";
            // 从版本中解析日期作为结束时间，开始时间为结束时间倒退7天
            String versionDateStr = version.substring(1); // 去掉v前缀: v20250827 -> 20250827
            String endDate = versionDateStr.substring(0, 4) + "-" + 
                           versionDateStr.substring(4, 6) + "-" + 
                           versionDateStr.substring(6, 8);
            
            // 计算开始时间（结束时间倒退7天）
            Date endDateObj = null;
            Date startDateObj = null;
            String startDate = null;
            try {
                endDateObj = DateFormatUtils.parseYMd(endDate);
                startDateObj = new Date(endDateObj.getTime() - 7 * 24 * 60 * 60 * 1000L);
                startDate = DateFormatUtils.formatYMd(startDateObj);
            } catch (Exception e) {
                logger.error("日期解析失败，使用当前日期: {}", e.getMessage());
                String currentDate = DateFormatUtils.formatYMd(new Date());
                startDate = currentDate;
                endDate = currentDate;
            }
            
            // 构建请求参数，模仿Web端的调用方式
            String uriWithArg = RestOperator.mergeUriArguments(
                    uri,
                    "schemaId", shop.getSchemaId(),
                    "shopId", String.valueOf(shop.getShopId()),
                    "startDate", startDate,
                    "endDate", endDate,
                    "flag", "1"
            );
            
            String msServiceId = RestOperator.getMSServiceId(shop.getDb(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            HttpEntity<Object> requestEntity = RestOperator.getJsonEntity(param);
            
            return popSubRestTemplate.postRest(msServiceId, uriWithArg, requestEntity);
            
        } catch (Exception e) {
            logger.error("调用Sub模块绩效接口失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查响应是否成功
     */
    private boolean isSuccessResponse(ApiResponse response) {
        return response != null && response.getData() != null;
    }

    /**
     * 解析绩效数据，从 avg.avg 对象中提取数据
     */
    private PerformanceData parsePerformanceData(ApiResponse response) {
        try {
            Object data = response.getData();
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;

                // 从 avg.avg 路径提取数据
                Object avgObj = dataMap.get("avg");
                if (avgObj instanceof Map) {
                    Map<String, Object> avgData = (Map<String, Object>) avgObj;
                    Object avgAvgObj = avgData.get("avg");
                    if (avgAvgObj instanceof Map) {
                        Map<String, Object> avgAvgData = (Map<String, Object>) avgAvgObj;
                        PerformanceData performanceData = new PerformanceData();

                        // 解析各个指标
                        performanceData.inquiryPaymentRate = parseDoubleValue(avgAvgData.get("queryToFinalPaid"));
                        performanceData.inquiryDeliveryRate = parseDoubleValue(avgAvgData.get("queryToOutStock"));
                        performanceData.firstResponseTime = parseDoubleValue(avgAvgData.get("avgRespTimeFirst"));
                        performanceData.avgResponseTime = parseDoubleValue(avgAvgData.get("avgRespTime"));
                        return performanceData;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析绩效数据失败: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 解析Double值，处理百分比等格式
     */
    private Double parseDoubleValue(Object value) {
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            } else if (value instanceof String) {
                String strValue = ((String) value).replace("%", "");
                return Double.parseDouble(strValue);
            }
        } catch (Exception e) {
            logger.debug("解析数值失败: {}", value);
        }
        
        return null;
    }

    /**
     * 检查是否包含极端值（0% 或 100%）
     */
    private boolean hasExtremeValues(PerformanceData data) {
        return isExtremeRateValue(data.inquiryPaymentRate) ||
               isExtremeRateValue(data.inquiryDeliveryRate) ||
               isExtremeResponseTime(data.firstResponseTime) ||
               isExtremeResponseTime(data.avgResponseTime);
    }

    /**
     * 检查转化率是否为极端值（0% 或 100%）
     */
    private boolean isExtremeRateValue(Double value) {
        if (value == null) {
            return true; // null值也视为极端值，排除该店铺
        }
        
        // 转化率检查是否为0%或100%（注意：这里的值可能是小数形式，如0.32表示32%）
        return value <= 0.0 || value >= 1.0;
    }
    
    /**
     * 检查响应时间是否为极端值
     */
    private boolean isExtremeResponseTime(Double value) {
        if (value == null) {
            return true; // null值也视为极端值，排除该店铺
        }
        
        // 响应时间检查是否为异常值（小于0或大于某个合理阈值，比如10分钟=600秒）
        return value <= 0.0 || value >= 600.0;
    }


    /**
     * 客服绩效数据内部类
     */
    private static class PerformanceData {
        Double inquiryPaymentRate;      // 询单-付款转化率
        Double inquiryDeliveryRate;     // 询单-出库转化率
        Double firstResponseTime;       // 首次平均响应时间
        Double avgResponseTime;         // 平均响应时间

        @Override
        public String toString() {
            return String.format("PerformanceData{询单付款率=%.2f%%, 询单出库率=%.2f%%, 首次响应时间=%.2f, 平均响应时间=%.2f}", 
                inquiryPaymentRate, inquiryDeliveryRate, firstResponseTime, avgResponseTime);
        }
    }

    /**
     * 店铺绩效数据内部类
     */
    private static class ShopPerformanceData {
        Double pvNum;                   // 浏览量
        Double uvNum;                   // 访客数
        Double shopDealPercent;         // 全店成交转化率
        Double shopOutStockPercent;     // 全店出库转化率
        Double consultPercent;          // 咨询率
        Double silenceDealPercent;      // 静默转化率

        @Override
        public String toString() {
            return String.format("ShopPerformanceData{浏览量=%.2f, 访客数=%.2f, 全店成交转化率=%.2f%%, 全店出库转化率=%.2f%%, 咨询率=%.2f%%, 静默转化率=%.2f%%}", 
                pvNum, uvNum, shopDealPercent, shopOutStockPercent, consultPercent, silenceDealPercent);
        }
    }

    // =================== 店铺均值相关方法 ===================


    /**
     * 检查店铺是否已参与当前店铺版本的计算
     */
    private boolean hasShopParticipatedInShopAvg(String version, Long shopId) {
        try {
            String shopsKey = REDIS_KEY_SHOP_PREFIX + version + ":shops";
            return redisOperator.isMember(shopsKey, shopId.toString());
        } catch (Exception e) {
            logger.error("检查店铺参与店铺版本状态失败: {}", e.getMessage(), e);
            return false; // 异常情况视为未参与
        }
    }

    /**
     * 标记店铺已参与店铺版本计算
     */
    private void markShopParticipatedInShopAvg(String version, Long shopId) {
        try {
            String shopsKey = REDIS_KEY_SHOP_PREFIX + version + ":shops";
            redisOperator.add(shopsKey, shopId.toString());
            redisOperator.expire(shopsKey, REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("标记店铺参与店铺版本状态失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 为指定版本处理单个店铺的店铺数据
     */
    private boolean processShopDataForShopVersion(JobShopQuery jobShopQuery, String version) {
        JobShopDTO shop = jobShopQuery.getShop();
        Long shopId = shop.getShopId();

        try {
            logger.debug("开始处理店铺: {} (店铺版本: {})", shopId, version);

            // 调用Sub模块获取店铺绩效数据
            ApiResponse response = callSubShopPerformanceApi(shop, version);
            
            if (response == null || !isSuccessResponse(response)) {
                logger.warn("店铺 {} 店铺绩效数据调用失败，跳过", shopId);
                return false;
            }

            // 解析并验证数据
            ShopPerformanceData shopPerformanceData = parseShopPerformanceData(response);
            if (shopPerformanceData == null) {
                logger.warn("店铺 {} 店铺绩效数据解析失败，跳过", shopId);
                return false;
            }

            // 检查极端值
            if (hasShopExtremeValues(shopPerformanceData)) {
                logger.debug("店铺 {} 存在店铺极端值，跳过: {}", shopId, shopPerformanceData);
                return false;
            }

            // 累加数据到当前版本的Redis
            accumulateShopDataToVersionedRedis(shopPerformanceData, version);
            
            // 标记店铺已参与计算
            markShopParticipatedInShopAvg(version, shopId);
            
            logger.debug("店铺 {} 店铺数据处理成功 (版本: {})", shopId, version);
            return true;

        } catch (Exception e) {
            logger.error("处理店铺 {} 店铺数据时发生异常: {}", shopId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 调用Sub模块的店铺绩效接口
     */
    private ApiResponse callSubShopPerformanceApi(JobShopDTO shop, String version) {
        try {
            String uri = "/shop/performance/selectShopPerformance";
            // 从版本中解析日期作为结束时间，开始时间为结束时间倒退7天
            String versionDateStr = version.substring(1); // 去掉v前缀: v20250827 -> 20250827
            String endDate = versionDateStr.substring(0, 4) + "-" + 
                           versionDateStr.substring(4, 6) + "-" + 
                           versionDateStr.substring(6, 8);
            
            // 计算开始时间（结束时间倒退7天）
            Date endDateObj = null;
            Date startDateObj = null;
            String startDate = null;
            try {
                endDateObj = DateFormatUtils.parseYMd(endDate);
                startDateObj = new Date(endDateObj.getTime() - 7 * 24 * 60 * 60 * 1000L);
                startDate = DateFormatUtils.formatYMd(startDateObj);
            } catch (Exception e) {
                logger.error("日期解析失败，使用当前日期: {}", e.getMessage());
                String currentDate = DateFormatUtils.formatYMd(new Date());
                startDate = currentDate;
                endDate = currentDate;
            }
            
            // 构建请求参数，模仿Web端的调用方式，但传递单个值而不是数组
            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("shop", convertJobShopToShopQuery(shop))
                    .put("dateType", 1)
                    .put("startDate", startDate)
                    .put("endDate", endDate)
                    .put("outStockValidDurationTime", "1")
                    .put("enquiryValidDurationTime", "2")
                    .put("property", "pvNum,uvNum,shopDealPercent,shopOutStockPercent,consultPercent,silenceDealPercent")
                    .put("filterTime", "")
                    .put("filter", "")
                    .toRequestEntity();
            
            String msServiceId = RestOperator.getMSServiceId(shop.getDb(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            
            return popSubRestTemplate.postRest(msServiceId, uri, body);
            
        } catch (Exception e) {
            logger.error("调用Sub模块店铺绩效接口失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换JobShopDTO为ShopQuery格式
     */
    private Map<String, Object> convertJobShopToShopQuery(JobShopDTO shop) {
        Map<String, Object> shopQuery = new HashMap<>();
        shopQuery.put("sellerNick", shop.getSellerNick());
        shopQuery.put("sessionKey", shop.getSessionKey());
        shopQuery.put("rtSchemaId", shop.getRtSchemaId());
        shopQuery.put("dbName", shop.getDb());
        shopQuery.put("rtDbName", shop.getRtDb());
       // shopQuery.put("sellerShowNick", shop.getSellerShowNick());
        shopQuery.put("subuserNum", shop.getSubuserNum());
        shopQuery.put("title", shop.getTitle());
       // shopQuery.put("lastGetDateTime", shop.getLastGetDateTime());
      //  shopQuery.put("sellerId", shop.getSellerId());
        shopQuery.put("schemaId", shop.getSchemaId());
        shopQuery.put("colType", shop.getColType());
        shopQuery.put("shopId", shop.getShopId());
        shopQuery.put("optionSessionKey", shop.getOptionSessionKey());
        return shopQuery;
    }

    /**
     * 解析店铺绩效数据
     */
    private ShopPerformanceData parseShopPerformanceData(ApiResponse response) {
        try {
            Object data = response.getData();
            if (data instanceof Map) {
                Map<String, Object> dataMap = (Map<String, Object>) data;
                Object avgObj = dataMap.get("avg");
                if (avgObj instanceof Map) {
                    Map<String, Object> avgData = (Map<String, Object>) avgObj;
                    ShopPerformanceData shopPerformanceData = new ShopPerformanceData();

                    // 解析各个指标
                    shopPerformanceData.pvNum = parseDoubleValue(avgData.get("pvNum"));
                    shopPerformanceData.uvNum = parseDoubleValue(avgData.get("uvNum"));
                    shopPerformanceData.shopDealPercent = parseDoubleValue(avgData.get("shopDealPercent"));
                    shopPerformanceData.shopOutStockPercent = parseDoubleValue(avgData.get("shopOutStockPercent"));
                    shopPerformanceData.consultPercent = parseDoubleValue(avgData.get("consultPercent"));
                    shopPerformanceData.silenceDealPercent = parseDoubleValue(avgData.get("silenceDealPercent"));
                    return shopPerformanceData;
                }
            }
        } catch (Exception e) {
            logger.error("解析店铺绩效数据失败: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 检查店铺数据是否包含极端值（0% 或 100%）
     */
    private boolean hasShopExtremeValues(ShopPerformanceData data) {
        return isExtremeRateValue(data.shopDealPercent) ||
               isExtremeRateValue(data.shopOutStockPercent) ||
               isExtremeRateValue(data.consultPercent) ||
               isExtremeRateValue(data.silenceDealPercent) ||
               isExtremeCountValue(data.pvNum) ||
               isExtremeCountValue(data.uvNum);
    }

    /**
     * 检查数量指标是否为极端值
     */
    private boolean isExtremeCountValue(Double value) {
        if (value == null) {
            return true; // null值也视为极端值，排除该店铺
        }
        
        // 数量指标检查是否为0或异常值
        return value <= 0.0;
    }

    /**
     * 累加店铺数据到指定版本的Redis
     */
    private void accumulateShopDataToVersionedRedis(ShopPerformanceData data, String version) {
        try {
            String prefix = REDIS_KEY_SHOP_PREFIX + version + ":";
            
            // 累加各项指标
            if (data.pvNum != null) {
                redisOperator.increment(prefix + "pvNum", data.pvNum);
                redisOperator.expire(prefix + "pvNum", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }
            
            if (data.uvNum != null) {
                redisOperator.increment(prefix + "uvNum", data.uvNum);
                redisOperator.expire(prefix + "uvNum", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }
            
            if (data.shopDealPercent != null) {
                redisOperator.increment(prefix + "shopDealPercent", data.shopDealPercent);
                redisOperator.expire(prefix + "shopDealPercent", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }
            
            if (data.shopOutStockPercent != null) {
                redisOperator.increment(prefix + "shopOutStockPercent", data.shopOutStockPercent);
                redisOperator.expire(prefix + "shopOutStockPercent", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }

            if (data.consultPercent != null) {
                redisOperator.increment(prefix + "consultPercent", data.consultPercent);
                redisOperator.expire(prefix + "consultPercent", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }

            if (data.silenceDealPercent != null) {
                redisOperator.increment(prefix + "silenceDealPercent", data.silenceDealPercent);
                redisOperator.expire(prefix + "silenceDealPercent", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            }

            // 增加店铺计数
            redisOperator.increment(prefix + "shop_count", 1L);
            redisOperator.expire(prefix + "shop_count", REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
            
        } catch (Exception e) {
            logger.error("累加店铺数据到版本化Redis失败: {}", e.getMessage(), e);
        }
    }

    public ApiResponse selectShopCsAndSystemSettings(Long shopId, String csGroup, String csNick, boolean needSystemSettings) {
        ApiResponse apiResponse;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId+"")
                .put("groupId", StringUtils.isBlank(csGroup)?"":csGroup)
                .put("csNick", StringUtils.isBlank(csNick)?"":csNick)
                .put("needSystemSettings", needSystemSettings)
                .toRequestEntity();
        final String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        try {
            final long start = System.currentTimeMillis();
            apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/manage/selectShopCsAndSystemSettings", body);
            final long end = System.currentTimeMillis();
            System.out.println("/shop/manage/selectShopCsAndSystemSettings>>>> totalTime ={"+(end-start)+"ms}");
        } catch (HttpClientErrorException e) {
            logger.error("CsShopSystemSettingsBusinessImpl error:{}",e.getMessage(),e);
            throw e;
        }
        return apiResponse;
    }
}