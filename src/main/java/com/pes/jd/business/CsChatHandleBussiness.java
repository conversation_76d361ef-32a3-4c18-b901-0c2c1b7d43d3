package com.pes.jd.business;

import com.pes.jd.model.BO.ChatDataPreparedStatusBO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

/**  
 * ClassName:CsChatHandleBussiness <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月2日 上午10:56:37 <br/>  
 * <AUTHOR> 
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface CsChatHandleBussiness {

	ChatDataPreparedStatusBO pullChatPeersAndChatlogs(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
			throws Exception;

}
  
