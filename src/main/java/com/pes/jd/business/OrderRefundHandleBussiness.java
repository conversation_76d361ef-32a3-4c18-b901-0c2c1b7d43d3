package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface OrderRefundHandleBussiness {

	// 订单退款  根据申请时间拉取
	void pullOrderRefundApplyData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	//根据审核时间拉取
	void pullOrderRefundCheckData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	//客服团队退款
	void handleShopCsTeamDailyRefund(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	//店铺每日的退款
	void handleShopDailyRefund(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	// 订单售后退款  根据申请时间拉取
	void pullAscServiceOrderRefundApplyData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	//订单售后退款  根据审核时间拉取
	void pullAscServiceOrderRefundCheckData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	//订单售后退款  补充数据
	void pullAscServiceOrderRefundDataByOrderId(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

}
