package com.pes.jd.exception;

public class DBNotExistException extends Exception{

	private String errorMsg;
	private String errorCode;
	
	public DBNotExistException() {
		super();
	}
	public DBNotExistException(String messsage) {
		super(messsage);
	}
	public String getErrorMsg() {
		return errorMsg;
	}
	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	@Override
	public String toString() {
		return "DBNotExistException [errorMsg=" + errorMsg + ", errorCode=" + errorCode + "]";
	}

	
}
