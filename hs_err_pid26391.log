#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x0000000000000000, pid=26391, tid=0x000000000000450f
#
# JRE version: OpenJDK Runtime Environment (8.0) (build 1.8.0_282b08-internal-202101310908-dcevm8u282b08)
# Java VM: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26 mixed mode bsd-amd64 compressed oops)
# Problematic frame:
# C  0x0000000000000000
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://github.com/dcevm/dcevm/issues
# The crash happened outside the Java Virtual Machine in native code.
# See problematic frame for where to report the bug.
#

---------------  T H R E A D  ---------------

Current thread (0x00007f7b5b852800):  JavaThread "SIGINT handler" daemon [_thread_in_native, id=17679, stack(0x000000030cfa2000,0x000000030d0a2000)]

siginfo: si_signo: 11 (SIGSEGV), si_code: 1 (SEGV_MAPERR), si_addr: 0x0000000000000000

Registers:
RAX=0x0000000000000000, RBX=0x00000001236537d8, RCX=0x0000000000000028, RDX=0x0000000000000000
RSP=0x000000030d0a18c8, RBP=0x000000030d0a1910, RSI=0x000000030d0a1920, RDI=0x00007f7b5b8529e0
R8 =0x00006000058d43c0, R9 =0x00000001236537d8, R10=0x000000010f1fc3f8, R11=0x000000010d76def2
R12=0x0000000000000000, R13=0x00000001236537d8, R14=0x000000030d0a1928, R15=0x00007f7b5b852800
RIP=0x0000000000000000, EFLAGS=0x0000000000000287, ERR=0x0000000000000004
  TRAPNO=0x000000000000000e

Top of Stack: (sp=0x000000030d0a18c8)
0x000000030d0a18c8:   000000010f1fc424 000000030d0a18d0
0x000000030d0a18d8:   00000001236537d8 000000030d0a1928
0x000000030d0a18e8:   0000000123653f40 0000000000000000
0x000000030d0a18f8:   00000001236537d8 0000000000000000
0x000000030d0a1908:   000000030d0a1930 000000030d0a1980
0x000000030d0a1918:   000000010f1e507d 0000000640809810
0x000000030d0a1928:   000000010f1f31b8 0000000000000001
0x000000030d0a1938:   0000000640809810 000000030d0a1930
0x000000030d0a1948:   0000000123653cb7 000000030d0a19b0
0x000000030d0a1958:   0000000123653f40 0000000000000000
0x000000030d0a1968:   00000001201b8410 000000030d0a1930
0x000000030d0a1978:   000000030d0a19b0 000000030d0a19f8
0x000000030d0a1988:   000000010f1e507d 0000000000000000
0x000000030d0a1998:   0000000000000000 0000000640809810
0x000000030d0a19a8:   0000000000000000 0000000000000082
0x000000030d0a19b8:   000000030d0a19b8 000000012016dcb0
0x000000030d0a19c8:   000000030d0a1a10 000000012016ddc8
0x000000030d0a19d8:   0000000000000000 000000012016dcc0
0x000000030d0a19e8:   000000030d0a19b0 000000030d0a1a08
0x000000030d0a19f8:   000000030d0a1a58 000000010f1e50c2
0x000000030d0a1a08:   0000000640960f70 0000000640865388
0x000000030d0a1a18:   000000030d0a1a18 0000000129fa6ba8
0x000000030d0a1a28:   000000030d0a1a68 0000000129fa6c50
0x000000030d0a1a38:   0000000000000000 0000000129fa6bb8
0x000000030d0a1a48:   000000030d0a1a08 000000030d0a1a70
0x000000030d0a1a58:   000000030d0a1b20 000000011050f1d4
0x000000030d0a1a68:   00000005889ea770 000000012000fbb8
0x000000030d0a1a78:   00007f7b5b852800 000000012000fbb8
0x000000030d0a1a88:   00007f7b5b852800 00000005889ea788
0x000000030d0a1a98:   000000010d520e36 000000030d0a1b20
0x000000030d0a1aa8:   000000010f1dd4e7 000000010f1dd4e7
0x000000030d0a1ab8:   00000005889ea788 0000000300001fa0 

Instructions: (pc=0x0000000000000000)
0xffffffffffffffe0:   

Register to memory mapping:

RAX=0x0000000000000000 is an unknown value
RBX={method} {0x00000001236537d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
RCX=0x0000000000000028 is an unknown value
RDX=0x0000000000000000 is an unknown value
RSP=0x000000030d0a18c8 is pointing into the stack for thread: 0x00007f7b5b852800
RBP=0x000000030d0a1910 is pointing into the stack for thread: 0x00007f7b5b852800
RSI=0x000000030d0a1920 is pointing into the stack for thread: 0x00007f7b5b852800
RDI=0x00007f7b5b8529e0 is an unknown value
R8 =0x00006000058d43c0 is an unknown value
R9 ={method} {0x00000001236537d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R10=0x000000010f1fc3f8 is at code_begin+1528 in an Interpreter codelet
method entry point (kind = native)  [0x000000010f1fbe00, 0x000000010f1fcd20]  3872 bytes
R11=0x000000010d76def2: throw_unsatisfied_link_error+0 in /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib at 0x000000010d394000
R12=0x0000000000000000 is an unknown value
R13={method} {0x00000001236537d8} 'beforeHalt' '()V' in 'java/lang/Shutdown'
R14=0x000000030d0a1928 is pointing into the stack for thread: 0x00007f7b5b852800
R15=0x00007f7b5b852800 is a thread


Stack: [0x000000030cfa2000,0x000000030d0a2000],  sp=0x000000030d0a18c8,  free space=1022k
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.Shutdown.beforeHalt()V+0
j  java.lang.Shutdown.exit(I)V+95
j  java.lang.Terminator$1.handle(Lsun/misc/Signal;)V+8
j  sun.misc.Signal$1.run()V+8
J 8323 C1 java.lang.Thread.run()V (17 bytes) @ 0x000000011050f1d4 [0x000000011050f180+0x54]
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
=>0x00007f7b5b852800 JavaThread "SIGINT handler" daemon [_thread_in_native, id=17679, stack(0x000000030cfa2000,0x000000030d0a2000)]
  0x00007f7bab710000 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=43631, stack(0x000000030ddcc000,0x000000030decc000)]
  0x00007f7b4b819800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=41971, stack(0x0000000310a50000,0x0000000310b50000)]
  0x00007f7b5c038000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=33103, stack(0x000000030f614000,0x000000030f714000)]
  0x00007f7b5b850000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=52519, stack(0x000000030eeff000,0x000000030efff000)]
  0x00007f7b2c3b7800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=37435, stack(0x000000030edfc000,0x000000030eefc000)]
  0x00007f7b5c02a000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=36759, stack(0x000000030ecf9000,0x000000030edf9000)]
  0x00007f7bbd442800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=26423, stack(0x000000030e9f0000,0x000000030eaf0000)]
  0x00007f7bbb620000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=37819, stack(0x000000030e3de000,0x000000030e4de000)]
  0x00007f7b2b17e000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_in_native, id=37155, stack(0x000000030dfd2000,0x000000030e0d2000)]
  0x00007f7b5b851800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=45803, stack(0x000000030eaf3000,0x000000030ebf3000)]
  0x00007f7b4c83b000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=49431, stack(0x0000000310e5c000,0x0000000310f5c000)]
  0x00007f7bbcb59000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=54239, stack(0x000000030e8ed000,0x000000030e9ed000)]
  0x00007f7b5c013000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=53399, stack(0x0000000310d59000,0x0000000310e59000)]
  0x00007f7bbcb56800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=86531, stack(0x000000030ebf6000,0x000000030ecf6000)]
  0x00007f7bbce28800 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=66067, stack(0x0000000310c56000,0x0000000310d56000)]
  0x00007f7b5b84a000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=51859, stack(0x0000000310747000,0x0000000310847000)]
  0x00007f7b2c382800 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=55127, stack(0x000000031094d000,0x0000000310a4d000)]
  0x00007f7b2b2a6000 JavaThread "com.alibaba.nacos.client.Worker.longPolling.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=55567, stack(0x000000031084a000,0x000000031094a000)]
  0x00007f7bbcb3c800 JavaThread "DestroyJavaVM" [_thread_blocked, id=4099, stack(0x000000030c8fa000,0x000000030c9fa000)]
  0x00007f7b2b275000 JavaThread "com.alibaba.nacos.naming.beat.sender" daemon [_thread_blocked, id=49159, stack(0x0000000310644000,0x0000000310744000)]
  0x00007f7bbcb3b800 JavaThread "http-nio-9200-AsyncTimeout" daemon [_thread_blocked, id=56323, stack(0x0000000310541000,0x0000000310641000)]
  0x00007f7bbcb12000 JavaThread "http-nio-9200-Acceptor-0" daemon [_thread_in_native, id=56835, stack(0x000000031043e000,0x000000031053e000)]
  0x00007f7b2b267000 JavaThread "http-nio-9200-ClientPoller-1" daemon [_thread_in_native, id=57863, stack(0x000000031033b000,0x000000031043b000)]
  0x00007f7bab7d8000 JavaThread "http-nio-9200-ClientPoller-0" daemon [_thread_in_native, id=58371, stack(0x0000000310238000,0x0000000310338000)]
  0x00007f7bab7d7000 JavaThread "http-nio-9200-exec-10" daemon [_thread_blocked, id=48131, stack(0x0000000310135000,0x0000000310235000)]
  0x00007f7b2b217800 JavaThread "http-nio-9200-exec-9" daemon [_thread_blocked, id=47875, stack(0x0000000310032000,0x0000000310132000)]
  0x00007f7b2c382000 JavaThread "http-nio-9200-exec-8" daemon [_thread_blocked, id=47619, stack(0x000000030ff2f000,0x000000031002f000)]
  0x00007f7bbd7b8000 JavaThread "http-nio-9200-exec-7" daemon [_thread_blocked, id=47107, stack(0x000000030fe2c000,0x000000030ff2c000)]
  0x00007f7bbd7b7800 JavaThread "http-nio-9200-exec-6" daemon [_thread_blocked, id=59395, stack(0x000000030fd29000,0x000000030fe29000)]
  0x00007f7b2c381000 JavaThread "http-nio-9200-exec-5" daemon [_thread_blocked, id=59907, stack(0x000000030fc26000,0x000000030fd26000)]
  0x00007f7bbd7ff800 JavaThread "http-nio-9200-exec-4" daemon [_thread_blocked, id=60163, stack(0x000000030fb23000,0x000000030fc23000)]
  0x00007f7b2c380800 JavaThread "http-nio-9200-exec-3" daemon [_thread_blocked, id=60675, stack(0x000000030fa20000,0x000000030fb20000)]
  0x00007f7b2c37f800 JavaThread "http-nio-9200-exec-2" daemon [_thread_blocked, id=60931, stack(0x000000030f91d000,0x000000030fa1d000)]
  0x00007f7b2c37b800 JavaThread "http-nio-9200-exec-1" daemon [_thread_blocked, id=62079, stack(0x000000030f81a000,0x000000030f91a000)]
  0x00007f7b2b25c800 JavaThread "NioBlockingSelector.BlockPoller-1" daemon [_thread_in_native, id=61875, stack(0x000000030f717000,0x000000030f817000)]
  0x00007f7b5b530000 JavaThread "ThreadPoolTaskScheduler-1" [_thread_blocked, id=62739, stack(0x000000030f511000,0x000000030f611000)]
  0x00007f7bbb514800 JavaThread "com.alibaba.nacos.naming.push.receiver" daemon [_thread_in_native, id=45071, stack(0x000000030f40e000,0x000000030f50e000)]
  0x00007f7bbb42f800 JavaThread "com.alibaba.nacos.naming.failover" daemon [_thread_blocked, id=64827, stack(0x000000030f30b000,0x000000030f40b000)]
  0x00007f7b5b544000 JavaThread "com.alibaba.nacos.naming.client.listener" daemon [_thread_blocked, id=65411, stack(0x000000030f208000,0x000000030f308000)]
  0x00007f7bbd592800 JavaThread "spring.cloud.inetutils" daemon [_thread_blocked, id=64291, stack(0x000000030f105000,0x000000030f205000)]
  0x00007f7bbd4bf800 JavaThread "commons-pool-evictor-thread" [_thread_blocked, id=64559, stack(0x000000030f002000,0x000000030f102000)]
  0x00007f7b4c020800 JavaThread "Druid-ConnectionPool-Destroy-38840646" daemon [_thread_blocked, id=36099, stack(0x000000030e0d5000,0x000000030e1d5000)]
  0x00007f7b5b843000 JavaThread "Druid-ConnectionPool-Create-38840646" daemon [_thread_blocked, id=41487, stack(0x000000030cd8f000,0x000000030ce8f000)]
  0x00007f7bbca98800 JavaThread "Abandoned connection cleanup thread" daemon [_thread_blocked, id=40015, stack(0x000000030e7ea000,0x000000030e8ea000)]
  0x00007f7b5b125800 JavaThread "container-0" [_thread_blocked, id=40255, stack(0x000000030e6e7000,0x000000030e7e7000)]
  0x00007f7b2b07a000 JavaThread "ContainerBackgroundProcessor[StandardEngine[Tomcat]]" daemon [_thread_blocked, id=41075, stack(0x000000030e5e4000,0x000000030e6e4000)]
  0x00007f7b5b3ff800 JavaThread "Thread-13" daemon [_thread_blocked, id=26171, stack(0x000000030e4e1000,0x000000030e5e1000)]
  0x00007f7bbc946000 JavaThread "com.alibaba.nacos.client.Worker.fixed-localhost_8848-5410605f-5cb8-42d9-99f8-eb12c5802955" daemon [_thread_blocked, id=42515, stack(0x000000030e2db000,0x000000030e3db000)]
  0x00007f7bbc439800 JavaThread "Timer-0" daemon [_thread_blocked, id=33391, stack(0x000000030e1d8000,0x000000030e2d8000)]
  0x00007f7bab409800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=27539, stack(0x000000030decf000,0x000000030dfcf000)]
  0x00007f7bab3e8000 JavaThread "Attach Listener" daemon [_thread_blocked, id=28683, stack(0x000000030dcc9000,0x000000030ddc9000)]
  0x00007f7bbd2d4000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=23843, stack(0x000000030dac3000,0x000000030dbc3000)]
  0x00007f7bbc197800 JavaThread "Service Thread" daemon [_thread_blocked, id=30211, stack(0x000000030d9c0000,0x000000030dac0000)]
  0x00007f7bbc196000 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=31235, stack(0x000000030d8bd000,0x000000030d9bd000)]
  0x00007f7bbb3c8800 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=22787, stack(0x000000030d7ba000,0x000000030d8ba000)]
  0x00007f7bab322000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=31491, stack(0x000000030d6b7000,0x000000030d7b7000)]
  0x00007f7bbc18c800 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=32023, stack(0x000000030d5b4000,0x000000030d6b4000)]
  0x00007f7bbca6e000 JavaThread "Thread-2" daemon [_thread_blocked, id=32263, stack(0x000000030d4b1000,0x000000030d5b1000)]
  0x00007f7bbca65000 JavaThread "HotSwap Dispatcher" daemon [_thread_blocked, id=19459, stack(0x000000030d3ae000,0x000000030d4ae000)]
  0x00007f7bbc16e000 JavaThread "HotSwap Watcher" daemon [_thread_blocked, id=19807, stack(0x000000030d2ab000,0x000000030d3ab000)]
  0x00007f7bbd135800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=17923, stack(0x000000030d0a5000,0x000000030d1a5000)]
  0x00007f7bbc8cf000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=20995, stack(0x000000030ce9f000,0x000000030cf9f000)]
  0x00007f7bbd139000 JavaThread "Finalizer" daemon [_thread_blocked, id=11551, stack(0x000000030cc03000,0x000000030cd03000)]
  0x00007f7b5b827000 JavaThread "Reference Handler" daemon [_thread_blocked, id=15395, stack(0x000000030cb00000,0x000000030cc00000)]

Other Threads:
  0x00007f7bbc818800 VMThread [stack: 0x000000030c9fd000,0x000000030cafd000] [id=15943]
  0x00007f7bbc279800 WatcherThread [stack: 0x000000030dbc6000,0x000000030dcc6000] [id=29423]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap:
 def new generation   total 177024K, used 151207K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,  90% used [0x0000000580000000, 0x0000000588b719e0, 0x00000005899b0000)
  from space 19648K,  42% used [0x000000058ace0000, 0x000000058b518618, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 80164K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9167K, capacity 9483K, committed 9600K, reserved 1048576K

Card table byte_map: [0x000000011e1dd000,0x000000011f3de000] byte_map_base: 0x000000011b5dd000

Polling page: 0x000000010c96f000

CodeCache: size=245760Kb used=19971Kb max_used=19971Kb free=225788Kb
 bounds [0x000000010f1dd000, 0x000000011056d000, 0x000000011e1dd000]
 total_blobs=8865 nmethods=8263 adapters=524
 compilation: enabled

Compilation events (10 events):
Event: 1240.248 Thread 0x00007f7bbc196000 8457       1       sun.misc.FloatingDecimal::getBinaryToASCIIConverter (183 bytes)
Event: 1240.249 Thread 0x00007f7bbc196000 nmethod 8457 0x000000011055fad0 code [0x000000011055fc60, 0x000000011055ffc8]
Event: 1240.260 Thread 0x00007f7bbc196000 8458   !   1       java.io.CharArrayWriter::write (125 bytes)
Event: 1240.262 Thread 0x00007f7bbc196000 nmethod 8458 0x0000000110560350 code [0x0000000110560500, 0x0000000110560be8]
Event: 1250.282 Thread 0x00007f7bbc196000 8459       1       java.lang.Double::isNaN (12 bytes)
Event: 1250.283 Thread 0x00007f7bbc196000 nmethod 8459 0x0000000110561110 code [0x0000000110561260, 0x0000000110561390]
Event: 1250.287 Thread 0x00007f7bbc196000 8460       1       java.lang.Double::toString (5 bytes)
Event: 1250.288 Thread 0x00007f7bbc196000 nmethod 8460 0x0000000110561410 code [0x0000000110561580, 0x00000001105616b8]
Event: 1250.297 Thread 0x00007f7bbc196000 8461       1       java.io.OutputStreamWriter::<init> (30 bytes)
Event: 1250.299 Thread 0x00007f7bbc196000 nmethod 8461 0x00000001105617d0 code [0x0000000110561980, 0x0000000110561d98]

GC Heap History (10 events):
Event: 25.589 GC heap before
{Heap before GC invocations=28 (full 3):
 def new generation   total 177024K, used 167650K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  52% used [0x00000005899b0000, 0x000000058a3b8b28, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 73367K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  18% used [0x0000000640000000, 0x00000006447a5ef8, 0x00000006447a6000, 0x0000000658000000)
 Metaspace       used 76142K, capacity 77224K, committed 77696K, reserved 1118208K
  class space    used 8716K, capacity 8986K, committed 9088K, reserved 1048576K
Event: 25.612 GC heap after
Heap after GC invocations=29 (full 3):
 def new generation   total 177024K, used 12693K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  64% used [0x000000058ace0000, 0x000000058b945658, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 75282K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x00000006449849b0, 0x0000000644984a00, 0x0000000658000000)
 Metaspace       used 76142K, capacity 77224K, committed 77696K, reserved 1118208K
  class space    used 8716K, capacity 8986K, committed 9088K, reserved 1048576K
}
Event: 26.143 GC heap before
{Heap before GC invocations=29 (full 3):
 def new generation   total 177024K, used 170069K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  64% used [0x000000058ace0000, 0x000000058b945658, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 75282K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x00000006449849b0, 0x0000000644984a00, 0x0000000658000000)
 Metaspace       used 78362K, capacity 79594K, committed 80000K, reserved 1120256K
  class space    used 8972K, capacity 9279K, committed 9344K, reserved 1048576K
Event: 26.164 GC heap after
Heap after GC invocations=30 (full 3):
 def new generation   total 177024K, used 9596K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  48% used [0x00000005899b0000, 0x000000058a30f0f8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 78362K, capacity 79594K, committed 80000K, reserved 1120256K
  class space    used 8972K, capacity 9279K, committed 9344K, reserved 1048576K
}
Event: 242.601 GC heap before
{Heap before GC invocations=30 (full 3):
 def new generation   total 177024K, used 166972K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  48% used [0x00000005899b0000, 0x000000058a30f0f8, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 80162K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9167K, capacity 9483K, committed 9600K, reserved 1048576K
Event: 242.664 GC heap after
Heap after GC invocations=31 (full 3):
 def new generation   total 177024K, used 9641K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  49% used [0x000000058ace0000, 0x000000058b64a768, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 80162K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9167K, capacity 9483K, committed 9600K, reserved 1048576K
}
Event: 584.942 GC heap before
{Heap before GC invocations=31 (full 3):
 def new generation   total 177024K, used 167017K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  49% used [0x000000058ace0000, 0x000000058b64a768, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 80162K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9167K, capacity 9483K, committed 9600K, reserved 1048576K
Event: 584.972 GC heap after
Heap after GC invocations=32 (full 3):
 def new generation   total 177024K, used 8450K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  43% used [0x00000005899b0000, 0x000000058a1f0970, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 80162K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9167K, capacity 9483K, committed 9600K, reserved 1048576K
}
Event: 937.661 GC heap before
{Heap before GC invocations=32 (full 3):
 def new generation   total 177024K, used 165826K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K, 100% used [0x0000000580000000, 0x00000005899b0000, 0x00000005899b0000)
  from space 19648K,  43% used [0x00000005899b0000, 0x000000058a1f0970, 0x000000058ace0000)
  to   space 19648K,   0% used [0x000000058ace0000, 0x000000058ace0000, 0x000000058c010000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 80162K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9167K, capacity 9483K, committed 9600K, reserved 1048576K
Event: 937.719 GC heap after
Heap after GC invocations=33 (full 3):
 def new generation   total 177024K, used 8417K [0x0000000580000000, 0x000000058c010000, 0x0000000640000000)
  eden space 157376K,   0% used [0x0000000580000000, 0x0000000580000000, 0x00000005899b0000)
  from space 19648K,  42% used [0x000000058ace0000, 0x000000058b518618, 0x000000058c010000)
  to   space 19648K,   0% used [0x00000005899b0000, 0x00000005899b0000, 0x000000058ace0000)
 tenured generation   total 393216K, used 78154K [0x0000000640000000, 0x0000000658000000, 0x00000007c0000000)
   the space 393216K,  19% used [0x0000000640000000, 0x0000000644c528a0, 0x0000000644c52a00, 0x0000000658000000)
 Metaspace       used 80162K, capacity 81458K, committed 81792K, reserved 1122304K
  class space    used 9167K, capacity 9483K, committed 9600K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Internal exceptions (10 events):
Event: 1205.525 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x000000058700a7d8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1205.526 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x000000058700ae58) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1215.528 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x0000000587837798) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1215.528 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x0000000587837e18) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1225.527 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x00000005878411f0) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1225.528 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x0000000587841870) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1235.532 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x0000000588221738) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1235.533 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x0000000588221db8) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1245.548 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x000000058822b190) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 1245.548 Thread 0x00007f7bbc439800 Exception <a 'java/io/FileNotFoundException'> (0x000000058822b810) thrown at [/private/var/lib/jenkins/workspace/DCEVM-8u161/jdk/JDK1.8.0_181-x64/k/product/oops/nocompressed/slave/dcevm-macosx/hotspot/src/share/vm/prims/jni.cpp, line 710]

Events (10 events):
Event: 1253.243 Executing VM operation: ChangeBreakpoints done
Event: 1253.244 Thread 0x00007f7b4c00d000 Thread exited: 0x00007f7b4c00d000
Event: 1253.244 Executing VM operation: ChangeBreakpoints
Event: 1253.244 Executing VM operation: ChangeBreakpoints done
Event: 1253.244 Executing VM operation: ChangeBreakpoints
Event: 1253.244 Executing VM operation: ChangeBreakpoints done
Event: 1253.252 Thread 0x00007f7bab183800 Thread exited: 0x00007f7bab183800
Event: 1253.294 loading class sun/misc/Signal$1
Event: 1253.296 loading class sun/misc/Signal$1 done
Event: 1253.298 Thread 0x00007f7b5b852800 Thread added: 0x00007f7b5b852800


Dynamic libraries:
0x000000000e0a8000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x000000000e0a8000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000000e0a8000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x000000000e0a8000 	/usr/lib/libSystem.B.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x000000000e0a8000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000000e0a8000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x000000000e0a8000 	/usr/lib/libspindump.dylib
0x000000000e0a8000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x000000000e0a8000 	/usr/lib/libbsm.0.dylib
0x000000000e0a8000 	/usr/lib/libapp_launch_measurement.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000000e0a8000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x000000000e0a8000 	/usr/lib/liblangid.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000000e0a8000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000000e0a8000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000000e0a8000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000000e0a8000 	/usr/lib/libz.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000000e0a8000 	/usr/lib/libicucore.A.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000000e0a8000 	/usr/lib/libMobileGestalt.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000000e0a8000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x000000000e0a8000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x000000000e0a8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000000e0a8000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000000e0a8000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000000e0a8000 	/usr/lib/libenergytrace.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x000000000e0a8000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000000e0a8000 	/usr/lib/libxml2.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x000000000e0a8000 	/usr/lib/libobjc.A.dylib
0x000000000e0a8000 	/usr/lib/libc++.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000000e0a8000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x000000000e0a8000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x000000000e0a8000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000000e0a8000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x000000000e0a8000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x000000000e0a8000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x000000000e0a8000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x000000000e0a8000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x000000000e0a8000 	/usr/lib/swift/libswiftAccelerate.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreImage.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDataDetection.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftDispatch.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftIOKit.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftMetal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftOSLog.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftObservation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftXPC.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_errno.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_math.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_signal.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_stdio.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftos.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsimd.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftsys_time.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftunistd.dylib
0x000000000e0a8000 	/usr/lib/libcompression.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x000000000e0a8000 	/usr/lib/libate.dylib
0x000000000e0a8000 	/usr/lib/system/libcache.dylib
0x000000000e0a8000 	/usr/lib/system/libcommonCrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libcompiler_rt.dylib
0x000000000e0a8000 	/usr/lib/system/libcopyfile.dylib
0x000000000e0a8000 	/usr/lib/system/libcorecrypto.dylib
0x000000000e0a8000 	/usr/lib/system/libdispatch.dylib
0x000000000e0a8000 	/usr/lib/system/libdyld.dylib
0x000000000e0a8000 	/usr/lib/system/libkeymgr.dylib
0x000000000e0a8000 	/usr/lib/system/libmacho.dylib
0x000000000e0a8000 	/usr/lib/system/libquarantine.dylib
0x000000000e0a8000 	/usr/lib/system/libremovefile.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_asl.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_blocks.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_c.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_collections.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_configuration.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwin.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_eligibility.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_featureflags.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_info.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_m.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_malloc.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_notify.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sandbox.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_sanitizers.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_secinit.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_kernel.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_platform.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_pthread.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_symptoms.dylib
0x000000000e0a8000 	/usr/lib/system/libsystem_trace.dylib
0x000000000e0a8000 	/usr/lib/system/libunwind.dylib
0x000000000e0a8000 	/usr/lib/system/libxpc.dylib
0x000000000e0a8000 	/usr/lib/libc++abi.dylib
0x000000000e0a8000 	/usr/lib/libRosetta.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000000e0a8000 	/usr/lib/liboah.dylib
0x000000000e0a8000 	/usr/lib/libfakelink.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x000000000e0a8000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x000000000e0a8000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000000e0a8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x000000000e0a8000 	/usr/lib/libapple_nghttp2.dylib
0x000000000e0a8000 	/usr/lib/libsqlite3.dylib
0x000000000e0a8000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x000000000e0a8000 	/usr/lib/system/libkxld.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x000000000e0a8000 	/usr/lib/libCoreEntitlements.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x000000000e0a8000 	/usr/lib/libcoretls.dylib
0x000000000e0a8000 	/usr/lib/libcoretls_cfhelpers.dylib
0x000000000e0a8000 	/usr/lib/libpam.2.dylib
0x000000000e0a8000 	/usr/lib/libxar.1.dylib
0x000000000e0a8000 	/usr/lib/libarchive.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000000e0a8000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftSystem.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x000000000e0a8000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x000000000e0a8000 	/usr/lib/libboringssl.dylib
0x000000000e0a8000 	/usr/lib/libdns_services.dylib
0x000000000e0a8000 	/usr/lib/libquic.dylib
0x000000000e0a8000 	/usr/lib/libusrtcp.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x000000000e0a8000 	/usr/lib/swift/libswiftDistributed.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSynchronization.dylib
0x000000000e0a8000 	/usr/lib/libnetwork.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000000e0a8000 	/usr/lib/libAppleArchive.dylib
0x000000000e0a8000 	/usr/lib/libbz2.1.0.dylib
0x000000000e0a8000 	/usr/lib/liblzma.5.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x000000000e0a8000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x000000000e0a8000 	/usr/lib/libgermantok.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x000000000e0a8000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000000e0a8000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000000e0a8000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000000e0a8000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x000000000e0a8000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x000000000e0a8000 	/usr/lib/libutil.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x000000000e0a8000 	/usr/lib/libhvf.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x000000000e0a8000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000000e0a8000 	/usr/lib/swift/libswift_RegexParser.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x000000000e0a8000 	/usr/lib/libexpat.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x000000000e0a8000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x000000000e0a8000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x000000000e0a8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x000000000e0a8000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000000e0a8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x000000000e0a8000 	/usr/lib/libcups.2.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000000e0a8000 	/usr/lib/libresolv.9.dylib
0x000000000e0a8000 	/usr/lib/libiconv.2.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x000000000e0a8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000000e0a8000 	/usr/lib/libheimdal-asn1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000000e0a8000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000000e0a8000 	/usr/lib/libcharset.1.dylib
0x000000000e0a8000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x000000000e0a8000 	/usr/lib/libAudioStatistics.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x000000000e0a8000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x000000000e0a8000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x000000000e0a8000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x000000000e0a8000 	/usr/lib/libSMC.dylib
0x000000000e0a8000 	/usr/lib/libAudioToolboxUtility.dylib
0x000000000e0a8000 	/usr/lib/libperfcheck.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x000000000e0a8000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x000000000e0a8000 	/usr/lib/libmis.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x000000000e0a8000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x000000000e0a8000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x000000000e0a8000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x000000000e0a8000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000000e0a8000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000000e0a8000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x000000000e0a8000 	/usr/lib/libAccessibility.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000000e0a8000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x000000000e0a8000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x000000000e0a8000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x000000000e0a8000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x000000000e0a8000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x000000000e0a8000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000000e0a8000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000000e0a8000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x000000000e0a8000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000000e0a8000 	/usr/lib/libIOReport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000000e0a8000 	/usr/lib/libTLE.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x000000000e0a8000 	/usr/lib/libmecab.dylib
0x000000000e0a8000 	/usr/lib/libCRFSuite.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000000e0a8000 	/usr/lib/libThaiTokenizer.dylib
0x000000000e0a8000 	/usr/lib/libCheckFix.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x000000000e0a8000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x000000000e0a8000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x000000000e0a8000 	/usr/lib/libxslt.1.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x000000000e0a8000 	/usr/lib/libcurl.4.dylib
0x000000000e0a8000 	/usr/lib/libcrypto.46.dylib
0x000000000e0a8000 	/usr/lib/libssl.48.dylib
0x000000000e0a8000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x000000000e0a8000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x000000000e0a8000 	/usr/lib/libsasl2.2.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftFoundation.dylib
0x000000000e0a8000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x000000000e0a8000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x000000000e0a8000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x000000010d394000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/dcevm/libjvm.dylib
0x000000000e0a8000 	/usr/lib/libstdc++.6.dylib
0x000000010c979000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libverify.dylib
0x000000010c9c7000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjava.dylib
0x000000010ca70000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libjdwp.dylib
0x000000010c999000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnpt.dylib
0x000000010cc3b000 	/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x000000010cb05000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libinstrument.dylib
0x000000010ca3a000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libzip.dylib
0x000000010d36d000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libdt_socket.dylib
0x000000012349e000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnio.dylib
0x00000001234da000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libnet.dylib
0x0000000123537000 	/Users/<USER>/.debugTools/lib/DebugToolsJniLibrary.dylib
0x000000012387b000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libmanagement.dylib
0x0000000128469000 	/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/libsunec.dylib

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:57247,suspend=y,server=n -agentpath:/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/master_2025_09_05_113700.jfr,log=/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/master_2025_09_05_113700.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dsocket.server.port=33833 -javaagent:/Users/<USER>/.debugTools/lib/debug-tools-agent-4.3.1.jar=server=false,printSql=Pretty,traceSql=false,hotswap=true,autoAttach=false,autoSaveSql=false,sqlRetentionDays=1 -Dfile.encoding=UTF-8 
java_command: com.pes.jd.application.UsrmApplication
java_class_path (initial): /Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/charsets.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/cldrdata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/dnsns.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/jaccess.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/localedata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/nashorn.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunec.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunjce_provider.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/sunpkcs11.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/ext/zipfs.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jce.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jfr.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/jsse.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/management-agent.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/resources.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/java8-debug-tools/Contents/Home/jre/lib/rt.jar:/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-master/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.17.RELEASE/spring-boot-starter-web-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.17.RELEASE/spring-boot-starter-tomcat-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repositor
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/Users/<USER>/.nvm/versions/node/v24.5.0/bin:/opt/homebrew/Cellar/node/24.1.0/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/.orbstack/bin
SHELL=/bin/zsh

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x45f382], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x388039], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x38958c], sa_mask[0]=00000000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x3897ec], sa_mask[0]=11111111111111111111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 24.6.0 Darwin Kernel Version 24.6.0: Mon Jul 14 11:28:30 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T6030 x86_64
rlimit: STACK 8176k, CORE 0k, NPROC 6000, NOFILE 10240, AS infinity
load average:4.21 4.91 4.63

CPU:total 12 (initial active 12) (1 cores per cpu, 1 threads per core) family 6 model 44 stepping 0, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, aes, clmul, tsc, tscinvbit, tscinv

Memory: 4k page, physical 37748736k(18660k free)

/proc/meminfo:


vm_info: Dynamic Code Evolution 64-Bit Server VM (25.71-b01-dcevmlight-26) for bsd-amd64 JRE (1.8.0), built on Jul 31 2018 03:28:48 by "jenkins" with gcc 4.8.2

time: Fri Sep  5 11:57:54 2025
elapsed time: 1253 seconds (0d 0h 20m 53s)

