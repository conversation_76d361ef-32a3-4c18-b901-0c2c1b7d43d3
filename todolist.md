# 行业均值功能开发 TodoList

## 项目概述
为客服绩效考核系统添加行业均值数据功能，包括4个关键指标的行业平均水平计算。

## 开发任务清单

### 阶段一：代码迁移与环境准备

- [ ] **1.1 分析现有代码结构**
  - [ ] 查看 Web 端报表接口实现代码
  - [ ] 分析需要迁移到 Job 的具体代码片段
  - [ ] 确定依赖的工具类和配置

- [ ] **1.2 迁移 Web 代码到 Job**
  - [ ] 复制相关的业务逻辑代码到 Job 模块
  - [ ] 调整代码以适配 Job 模块的环境
  - [ ] 修改远程调用目标为 Sub 模块（绕过登录验证）
  - [ ] 充分利用job中已有的代码

- [ ] **1.3 配置 Redis 连接**
	- [ ] 查看job中已有的redis代码使用方法
  - [ ] 确认 Job 模块的 Redis 配置

### 阶段二：核心功能实现

- [ ] **2.1 实现日期检查逻辑**
  - [ ] 创建方法检查 `industry_avg:last_update_date`
  - [ ] 实现7天时间间隔判断逻辑
  - [ ] 添加清空过期数据的功能

- [ ] **2.2 实现店铺数据收集(难点)**
  - [ ] 获取所有店铺列表（无条件限制）
  - [ ] 实现单个店铺报表数据调用
  - [ ] 解析返回的4个关键指标数据

- [ ] **2.3 实现极端值过滤**
  - [ ] 检查4个指标是否存在0%或100%的极端值
  - [ ] 实现过滤逻辑（任一极端值则排除整个店铺）
  - [ ] 记录被过滤店铺的日志信息

- [ ] **2.4 实现 Redis 数据累加**
  - [ ] 实现以下Key的数据累加逻辑：
    - [ ] `industry_avg:inquiry_payment_rate`
    - [ ] `industry_avg:inquiry_delivery_rate`  
    - [ ] `industry_avg:first_response_time`
    - [ ] `industry_avg:avg_response_time`
    - [ ] `industry_avg:shop_count`
  - [ ] 更新 `industry_avg:last_update_date`
  - [ ] 设置所有Key的过期时间为14天

### 阶段三：集成与优化

- [ ] **3.1 集成到现有Job任务**
  - [ ] 在 `DataPrepareBusinessImpl.java:514-540` 位置添加功能调用
  - [ ] 确保在每日店铺任务末尾执行
  - [ ] 测试与现有任务的兼容性

- [ ] **3.2 异常处理实现**
  - [ ] 实现接口调用失败的异常捕获
  - [ ] 实现Redis操作失败的异常处理
  - [ ] 添加跳过异常店铺继续执行的逻辑

- [ ] **3.3 日志系统完善**
  - [ ] 添加任务开始和结束日志
  - [ ] 记录处理的店铺总数
  - [ ] 记录跳过的店铺数量及原因
  - [ ] 记录异常情况的详细信息

### 阶段四：测试与验证

- [ ] **4.1 单元测试**
  - [ ] 在JobCenterController添加一个接口,模仿已有代码来测试这个功能点
  

## 完成标准

✅ **任务完成标准**：
- 所有店铺数据能正确收集并累加到Redis
- 极端值过滤逻辑正常工作
- 7天更新周期准确触发
- 异常处理不影响整体任务执行
- 日志记录完整清晰

## 使用说明

### 标记任务完成
将 `- [ ]` 改为 `- [x]` 即可标记任务完成

### 添加备注
可在任务后添加备注，格式：`- [x] 任务名称 ✅ 完成时间：2025-08-27 备注：具体实现细节`

### 跳过任务
如果某个任务不需要执行，可标记为：`- [~]` 并添加跳过原因