## 系统概述
该系统是一个客服绩效考核系统，主要由以下四个模块组成：
### 系统架构
| 模块 | 功能描述                                                  | 项目路径 |
|------|-------------------------------------------------------|----------|
| **job** | 每日拉取京东数据，进行计算并存库（客服聊天数据、店铺订单数据、客服订单绑定关系等）大多提供数据给sub使用 | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob` |
| **web** | 接口接纳层，转发请求至 master 或 sub，不直接查询数据库                     | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-web` |
| **sub** | 承载 web 接口请求，查询数据库并组装数据返回给 web                         | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailysub` |
| **master** | 存储用户购买产品后的相关数据（客服信息、店铺信息、权限等）                         | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-master` |
我们这个系统采用分库分表来设计.每一个job与sub服务都只对应一个db的scheme.每个店铺的db与scheme数据存储在master中.分表主要根据店铺数据的日期来分,一般按照拉取数据的月份来分.

### 数据流转
前端请求 → web模块 → 路由判断 → sub/master模块 → 数据库查询 → 响应返回
参考"apiResponse = popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SELECTORDERPRESALELST.getName(), body);""
接口位置：每个模块都有独立的rest包，包含远程调用接口
调用方式：使用RestTemplate进行HTTP调用
请求枚举：通过RequestUrlEnum统一管理接口URL
参数传递：请求参数封装在body中传递

代码查找建议
如不确定具体实现方式，建议搜索现有代码中的关键词：

popSubRestTemplate.postRest - sub模块调用示例
RequestUrlEnum - 接口URL枚举
rest包 - 远程调用接口定义

### 开发要求
要求按照java1.8的语法进行开发

### 开发规范
#### ApiResponse使用规范
- 使用`ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001)`表示成功
- 使用`ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002)`表示失败
- 带消息：`ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002, "错误信息")`
- 带数据：`ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result)`

#### master模块分层架构
- Controller层：处理HTTP请求，调用Business层
- Business层：业务接口及实现类（BusinessImpl）
- Dao层：数据访问接口及实现类（DaoImpl）
- Mapper层：MyBatis映射接口（Mapper.java + Mapper.xml）
- 实体层：DO（数据对象）、DTO（传输对象）、Param（参数对象）

#### 命名规范
- Business实现类：`@Service("xxxBusiness")`
- Dao实现类：`@Repository("xxxDao")`
- Mapper接口：位于`mapper.main`包，XML文件对应namespace
- 依赖注入：使用`@Resource`注解
- 实体类生成多用lombook注解,还要写上字段注释

#### 参数传递规范
- **禁止使用Map传参**：Controller方法必须使用具体的Param对象接收参数
- **类型安全**：避免Map的类型转换，使用强类型参数对象
- **参数对象要求**：
  - 使用`@Data`注解简化getter/setter
  - 每个字段必须添加注释说明用途
  - 提供无参构造函数和全参构造函数

#### 登录用户专属字段的获取
- 前端参数不可信任
- 后端接受登录用户专属字段时,不取前端的,继承的BaseController中有专门的函数获取用户基本信息