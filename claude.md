## 系统概述
该系统是一个客服绩效考核系统，主要由以下四个模块组成：
### 系统架构
| 模块 | 功能描述                                                  | 项目路径 |
|------|-------------------------------------------------------|----------|
| **job** | 每日拉取京东数据，进行计算并存库（客服聊天数据、店铺订单数据、客服订单绑定关系等）大多提供数据给sub使用 | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailyjob` |
| **web** | 接口接纳层，转发请求至 master 或 sub，不直接查询数据库                     | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-web` |
| **sub** | 承载 web 接口请求，查询数据库并组装数据返回给 web                         | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-pop-dailysub` |
| **master** | 存储用户购买产品后的相关数据（客服信息、店铺信息、权限等）                         | `/Users/<USER>/code/java/MyjavaProject/IdeaProjects/yiyitech-master` |
我们这个系统采用分库分表来设计.每一个job与sub服务都只对应一个db的scheme.每个店铺的db与scheme数据存储在master中.分表主要根据店铺数据的日期来分,一般按照拉取数据的月份来分.

### 数据流转
前端请求 → web模块 → 路由判断 → sub/master模块 → 数据库查询 → 响应返回
参考"apiResponse = popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SELECTORDERPRESALELST.getName(), body);""
接口位置：每个模块都有独立的rest包，包含远程调用接口
调用方式：使用RestTemplate进行HTTP调用
请求枚举：通过RequestUrlEnum统一管理接口URL
参数传递：请求参数封装在body中传递

代码查找建议
如不确定具体实现方式，建议搜索现有代码中的关键词：

popSubRestTemplate.postRest - sub模块调用示例
RequestUrlEnum - 接口URL枚举
rest包 - 远程调用接口定义

### 开发要求
要求按照java1.8的语法进行开发